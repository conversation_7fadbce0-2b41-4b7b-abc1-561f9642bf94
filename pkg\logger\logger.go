package logger

import (
	"os"
	"strings"

	"github.com/sirupsen/logrus"
)

// Logger wraps logrus.Logger with additional functionality
type Logger struct {
	*logrus.Logger
}

// New creates a new logger instance with the specified log level
func New(level string) *Logger {
	log := logrus.New()
	
	// Set log level
	logLevel, err := logrus.ParseLevel(strings.ToLower(level))
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	log.SetLevel(logLevel)
	
	// Set formatter
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		ForceColors:   true,
	})
	
	// Set output
	log.SetOutput(os.Stdout)
	
	return &Logger{Logger: log}
}

// WithComponent adds a component field to the logger
func (l *Logger) WithComponent(component string) *logrus.Entry {
	return l.WithField("component", component)
}

// WithTraceID adds a trace ID field to the logger
func (l *Logger) WithTraceID(traceID string) *logrus.Entry {
	return l.WithField("trace_id", traceID)
}

// WithSpanID adds a span ID field to the logger
func (l *Logger) WithSpanID(spanID string) *logrus.Entry {
	return l.WithField("span_id", spanID)
}

// WithError adds an error field to the logger
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}
