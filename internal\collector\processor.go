package collector

import (
	"context"
	"fmt"
	"sync"
	"time"

	"distributed-tracing-tool/internal/models"
	"distributed-tracing-tool/internal/storage"
	"distributed-tracing-tool/pkg/logger"
)

// Processor handles processing of collected trace data
type Processor struct {
	logger  *logger.Logger
	storage storage.Storage
	config  *ProcessorConfig
	
	// Batching
	traceBatch []*models.Trace
	spanBatch  []*models.Span
	batchMutex sync.Mutex
	
	// Background processing
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	
	// Channels for async processing
	traceQueue chan *models.Trace
	spanQueue  chan *models.Span
	
	// Metrics
	metrics *ProcessorMetrics
}

// ProcessorConfig holds configuration for the processor
type ProcessorConfig struct {
	BatchSize       int           `json:"batchSize"`
	BatchTimeout    time.Duration `json:"batchTimeout"`
	QueueSize       int           `json:"queueSize"`
	WorkerCount     int           `json:"workerCount"`
	ValidateData    bool          `json:"validateData"`
	SanitizeData    bool          `json:"sanitizeData"`
	EnableMetrics   bool          `json:"enableMetrics"`
}

// ProcessorMetrics holds metrics for the processor
type ProcessorMetrics struct {
	TracesProcessed   int64 `json:"tracesProcessed"`
	SpansProcessed    int64 `json:"spansProcessed"`
	TracesDropped     int64 `json:"tracesDropped"`
	SpansDropped      int64 `json:"spansDropped"`
	ValidationErrors  int64 `json:"validationErrors"`
	ProcessingErrors  int64 `json:"processingErrors"`
	AverageLatency    int64 `json:"averageLatency"` // microseconds
	QueueDepth        int   `json:"queueDepth"`
}

// DefaultProcessorConfig returns default processor configuration
func DefaultProcessorConfig() *ProcessorConfig {
	return &ProcessorConfig{
		BatchSize:     1000,
		BatchTimeout:  5 * time.Second,
		QueueSize:     10000,
		WorkerCount:   4,
		ValidateData:  true,
		SanitizeData:  true,
		EnableMetrics: true,
	}
}

// NewProcessor creates a new processor
func NewProcessor(logger *logger.Logger, storage storage.Storage, config *ProcessorConfig) *Processor {
	if config == nil {
		config = DefaultProcessorConfig()
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	p := &Processor{
		logger:     logger,
		storage:    storage,
		config:     config,
		ctx:        ctx,
		cancel:     cancel,
		traceQueue: make(chan *models.Trace, config.QueueSize),
		spanQueue:  make(chan *models.Span, config.QueueSize),
		metrics:    &ProcessorMetrics{},
	}
	
	// Start background workers
	p.startWorkers()
	
	return p
}

// ProcessTrace processes a single trace
func (p *Processor) ProcessTrace(ctx context.Context, trace *models.Trace) error {
	start := time.Now()
	defer func() {
		if p.config.EnableMetrics {
			latency := time.Since(start).Microseconds()
			p.updateLatency(latency)
		}
	}()
	
	// Validate trace if enabled
	if p.config.ValidateData {
		if err := models.ValidateTrace(trace); err != nil {
			p.metrics.ValidationErrors++
			return fmt.Errorf("trace validation failed: %w", err)
		}
	}
	
	// Sanitize trace if enabled
	if p.config.SanitizeData {
		models.SanitizeTrace(trace)
	}
	
	// Try to send to queue for async processing
	select {
	case p.traceQueue <- trace:
		return nil
	default:
		// Queue is full, process synchronously
		p.logger.Warn("Trace queue full, processing synchronously")
		return p.processTraceSync(ctx, trace)
	}
}

// ProcessSpan processes a single span
func (p *Processor) ProcessSpan(ctx context.Context, span *models.Span) error {
	start := time.Now()
	defer func() {
		if p.config.EnableMetrics {
			latency := time.Since(start).Microseconds()
			p.updateLatency(latency)
		}
	}()
	
	// Validate span if enabled
	if p.config.ValidateData {
		if err := models.ValidateSpan(span); err != nil {
			p.metrics.ValidationErrors++
			return fmt.Errorf("span validation failed: %w", err)
		}
	}
	
	// Sanitize span if enabled
	if p.config.SanitizeData {
		models.SanitizeSpan(span)
	}
	
	// Try to send to queue for async processing
	select {
	case p.spanQueue <- span:
		return nil
	default:
		// Queue is full, process synchronously
		p.logger.Warn("Span queue full, processing synchronously")
		return p.processSpanSync(ctx, span)
	}
}

// ProcessBatch processes a batch of traces and spans
func (p *Processor) ProcessBatch(ctx context.Context, traces []*models.Trace, spans []*models.Span) error {
	var errors []error
	
	// Process traces
	for _, trace := range traces {
		if err := p.ProcessTrace(ctx, trace); err != nil {
			errors = append(errors, fmt.Errorf("failed to process trace %s: %w", trace.TraceID, err))
		}
	}
	
	// Process spans
	for _, span := range spans {
		if err := p.ProcessSpan(ctx, span); err != nil {
			errors = append(errors, fmt.Errorf("failed to process span %s: %w", span.SpanID, err))
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("batch processing failed with %d errors: %v", len(errors), errors[0])
	}
	
	return nil
}

// GetMetrics returns current processor metrics
func (p *Processor) GetMetrics() *ProcessorMetrics {
	p.metrics.QueueDepth = len(p.traceQueue) + len(p.spanQueue)
	return p.metrics
}

// Stop stops the processor and waits for all workers to finish
func (p *Processor) Stop() error {
	p.logger.Info("Stopping processor...")
	
	// Cancel context to stop workers
	p.cancel()
	
	// Close queues
	close(p.traceQueue)
	close(p.spanQueue)
	
	// Wait for workers to finish
	p.wg.Wait()
	
	// Flush any remaining batches
	if err := p.flushBatches(); err != nil {
		return fmt.Errorf("failed to flush batches: %w", err)
	}
	
	p.logger.Info("Processor stopped")
	return nil
}

// startWorkers starts background processing workers
func (p *Processor) startWorkers() {
	// Start trace workers
	for i := 0; i < p.config.WorkerCount; i++ {
		p.wg.Add(1)
		go p.traceWorker()
	}
	
	// Start span workers
	for i := 0; i < p.config.WorkerCount; i++ {
		p.wg.Add(1)
		go p.spanWorker()
	}
	
	// Start batch flusher
	p.wg.Add(1)
	go p.batchFlusher()
}

// traceWorker processes traces from the queue
func (p *Processor) traceWorker() {
	defer p.wg.Done()
	
	for {
		select {
		case trace, ok := <-p.traceQueue:
			if !ok {
				return
			}
			
			if err := p.addToBatch(trace, nil); err != nil {
				p.logger.WithError(err).Error("Failed to add trace to batch")
				p.metrics.TracesDropped++
			}
			
		case <-p.ctx.Done():
			return
		}
	}
}

// spanWorker processes spans from the queue
func (p *Processor) spanWorker() {
	defer p.wg.Done()
	
	for {
		select {
		case span, ok := <-p.spanQueue:
			if !ok {
				return
			}
			
			if err := p.addToBatch(nil, span); err != nil {
				p.logger.WithError(err).Error("Failed to add span to batch")
				p.metrics.SpansDropped++
			}
			
		case <-p.ctx.Done():
			return
		}
	}
}

// batchFlusher periodically flushes batches to storage
func (p *Processor) batchFlusher() {
	defer p.wg.Done()
	
	ticker := time.NewTicker(p.config.BatchTimeout)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			if err := p.flushBatches(); err != nil {
				p.logger.WithError(err).Error("Failed to flush batches")
			}
			
		case <-p.ctx.Done():
			return
		}
	}
}

// addToBatch adds trace or span to the appropriate batch
func (p *Processor) addToBatch(trace *models.Trace, span *models.Span) error {
	p.batchMutex.Lock()
	defer p.batchMutex.Unlock()
	
	if trace != nil {
		p.traceBatch = append(p.traceBatch, trace)
		if len(p.traceBatch) >= p.config.BatchSize {
			return p.flushTraceBatch()
		}
	}
	
	if span != nil {
		p.spanBatch = append(p.spanBatch, span)
		if len(p.spanBatch) >= p.config.BatchSize {
			return p.flushSpanBatch()
		}
	}
	
	return nil
}

// flushBatches flushes all pending batches
func (p *Processor) flushBatches() error {
	p.batchMutex.Lock()
	defer p.batchMutex.Unlock()
	
	var errors []error
	
	if err := p.flushTraceBatch(); err != nil {
		errors = append(errors, err)
	}
	
	if err := p.flushSpanBatch(); err != nil {
		errors = append(errors, err)
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("flush errors: %v", errors)
	}
	
	return nil
}

// flushTraceBatch flushes the trace batch to storage
func (p *Processor) flushTraceBatch() error {
	if len(p.traceBatch) == 0 {
		return nil
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := p.storage.WriteBatch(ctx, p.traceBatch); err != nil {
		p.metrics.ProcessingErrors++
		return fmt.Errorf("failed to write trace batch: %w", err)
	}
	
	p.metrics.TracesProcessed += int64(len(p.traceBatch))
	p.logger.WithField("count", len(p.traceBatch)).Debug("Flushed trace batch")
	
	// Clear batch
	p.traceBatch = p.traceBatch[:0]
	
	return nil
}

// flushSpanBatch flushes the span batch to storage
func (p *Processor) flushSpanBatch() error {
	if len(p.spanBatch) == 0 {
		return nil
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := p.storage.WriteSpanBatch(ctx, p.spanBatch); err != nil {
		p.metrics.ProcessingErrors++
		return fmt.Errorf("failed to write span batch: %w", err)
	}
	
	p.metrics.SpansProcessed += int64(len(p.spanBatch))
	p.logger.WithField("count", len(p.spanBatch)).Debug("Flushed span batch")
	
	// Clear batch
	p.spanBatch = p.spanBatch[:0]
	
	return nil
}

// processTraceSync processes a trace synchronously
func (p *Processor) processTraceSync(ctx context.Context, trace *models.Trace) error {
	if err := p.storage.WriteTrace(ctx, trace); err != nil {
		p.metrics.ProcessingErrors++
		return fmt.Errorf("failed to write trace: %w", err)
	}
	
	p.metrics.TracesProcessed++
	return nil
}

// processSpanSync processes a span synchronously
func (p *Processor) processSpanSync(ctx context.Context, span *models.Span) error {
	if err := p.storage.WriteSpan(ctx, span); err != nil {
		p.metrics.ProcessingErrors++
		return fmt.Errorf("failed to write span: %w", err)
	}
	
	p.metrics.SpansProcessed++
	return nil
}

// updateLatency updates the average latency metric
func (p *Processor) updateLatency(latency int64) {
	// Simple moving average (in production, use more sophisticated metrics)
	if p.metrics.AverageLatency == 0 {
		p.metrics.AverageLatency = latency
	} else {
		p.metrics.AverageLatency = (p.metrics.AverageLatency + latency) / 2
	}
}
