package sqlite

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"distributed-tracing-tool/internal/models"
	"distributed-tracing-tool/internal/storage"
)

// FindTraces finds traces based on query parameters
func (s *SQLiteStorage) FindTraces(ctx context.Context, query *storage.TraceQuery) ([]*models.Trace, error) {
	sqlQuery, args := s.buildTraceQuery(query)
	
	rows, err := s.db.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute trace query: %w", err)
	}
	defer rows.Close()
	
	var traces []*models.Trace
	for rows.Next() {
		trace, err := s.scanTraceMetadata(rows)
		if err != nil {
			s.logger.WithError(err).Error("Failed to scan trace")
			continue
		}
		
		// Load processes and spans for each trace
		processes, err := s.getTraceProcesses(ctx, trace.TraceID)
		if err != nil {
			s.logger.WithError(err).WithField("trace_id", trace.TraceID).Error("Failed to load processes")
		} else {
			trace.Processes = processes
		}
		
		spans, err := s.GetTraceSpans(ctx, trace.TraceID)
		if err != nil {
			s.logger.WithError(err).WithField("trace_id", trace.TraceID).Error("Failed to load spans")
		} else {
			trace.Spans = spans
		}
		
		traces = append(traces, trace)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating traces: %w", err)
	}
	
	return traces, nil
}

// FindSpans finds spans based on query parameters
func (s *SQLiteStorage) FindSpans(ctx context.Context, query *storage.SpanQuery) ([]*models.Span, error) {
	sqlQuery, args := s.buildSpanQuery(query)
	
	rows, err := s.db.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute span query: %w", err)
	}
	defer rows.Close()
	
	var spans []*models.Span
	for rows.Next() {
		span, err := s.scanSpan(rows)
		if err != nil {
			s.logger.WithError(err).Error("Failed to scan span")
			continue
		}
		spans = append(spans, span)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating spans: %w", err)
	}
	
	return spans, nil
}

// GetServices returns all unique service names
func (s *SQLiteStorage) GetServices(ctx context.Context) ([]string, error) {
	query := `SELECT DISTINCT service_name FROM processes ORDER BY service_name`
	
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query services: %w", err)
	}
	defer rows.Close()
	
	var services []string
	for rows.Next() {
		var service string
		if err := rows.Scan(&service); err != nil {
			s.logger.WithError(err).Error("Failed to scan service")
			continue
		}
		services = append(services, service)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating services: %w", err)
	}
	
	return services, nil
}

// GetOperations returns all unique operations for a service
func (s *SQLiteStorage) GetOperations(ctx context.Context, service string) ([]string, error) {
	query := `
		SELECT DISTINCT s.operation_name 
		FROM spans s
		JOIN processes p ON s.trace_id = p.trace_id AND s.process_id = p.process_id
		WHERE p.service_name = ?
		ORDER BY s.operation_name
	`
	
	rows, err := s.db.QueryContext(ctx, query, service)
	if err != nil {
		return nil, fmt.Errorf("failed to query operations: %w", err)
	}
	defer rows.Close()
	
	var operations []string
	for rows.Next() {
		var operation string
		if err := rows.Scan(&operation); err != nil {
			s.logger.WithError(err).Error("Failed to scan operation")
			continue
		}
		operations = append(operations, operation)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating operations: %w", err)
	}
	
	return operations, nil
}

// GetTraceStats returns statistics for a trace
func (s *SQLiteStorage) GetTraceStats(ctx context.Context, traceID models.TraceID) (*storage.TraceStats, error) {
	query := `
		SELECT trace_id, start_time, end_time, duration, service_count, span_count, error_count
		FROM traces 
		WHERE trace_id = ?
	`
	
	row := s.db.QueryRowContext(ctx, query, traceID)
	
	var stats storage.TraceStats
	var startTime, endTime int64
	
	err := row.Scan(
		&stats.TraceID,
		&startTime,
		&endTime,
		&stats.Duration,
		&stats.ServiceCount,
		&stats.SpanCount,
		&stats.ErrorCount,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trace not found: %s", traceID)
		}
		return nil, fmt.Errorf("failed to scan trace stats: %w", err)
	}
	
	stats.StartTime = time.UnixMicro(startTime)
	stats.EndTime = time.UnixMicro(endTime)
	
	return &stats, nil
}

// GetServiceStats returns statistics for a service
func (s *SQLiteStorage) GetServiceStats(ctx context.Context, service string, timeRange storage.TimeRange) (*storage.ServiceStats, error) {
	// This is a simplified implementation
	// In a production system, you'd want more sophisticated aggregation
	
	query := `
		SELECT 
			COUNT(*) as request_count,
			SUM(CASE WHEN error_count > 0 THEN 1 ELSE 0 END) as error_count,
			AVG(duration) as avg_duration
		FROM traces t
		JOIN processes p ON t.trace_id = p.trace_id
		WHERE p.service_name = ? 
		AND t.start_time >= ? 
		AND t.start_time <= ?
	`
	
	row := s.db.QueryRowContext(ctx, query, service, timeRange.Start.UnixMicro(), timeRange.End.UnixMicro())
	
	var requestCount, errorCount int64
	var avgDuration float64
	
	err := row.Scan(&requestCount, &errorCount, &avgDuration)
	if err != nil {
		return nil, fmt.Errorf("failed to scan service stats: %w", err)
	}
	
	errorRate := float64(0)
	if requestCount > 0 {
		errorRate = float64(errorCount) / float64(requestCount) * 100
	}
	
	stats := &storage.ServiceStats{
		Service:      service,
		RequestCount: requestCount,
		ErrorCount:   errorCount,
		ErrorRate:    errorRate,
		AvgDuration:  int64(avgDuration),
		TimeRange:    timeRange,
		// P95Duration and P99Duration would require more complex queries
		P95Duration: int64(avgDuration * 1.5), // Simplified approximation
		P99Duration: int64(avgDuration * 2.0), // Simplified approximation
	}
	
	return stats, nil
}

// DeleteOldTraces deletes traces older than the specified time
func (s *SQLiteStorage) DeleteOldTraces(ctx context.Context, before time.Time) (int64, error) {
	query := `DELETE FROM traces WHERE start_time < ?`
	
	result, err := s.db.ExecContext(ctx, query, before.UnixMicro())
	if err != nil {
		return 0, fmt.Errorf("failed to delete old traces: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	return rowsAffected, nil
}

// GetStorageStats returns storage statistics
func (s *SQLiteStorage) GetStorageStats(ctx context.Context) (*storage.StorageStats, error) {
	var stats storage.StorageStats
	
	// Count traces
	row := s.db.QueryRowContext(ctx, `SELECT COUNT(*) FROM traces`)
	if err := row.Scan(&stats.TraceCount); err != nil {
		return nil, fmt.Errorf("failed to count traces: %w", err)
	}
	
	// Count spans
	row = s.db.QueryRowContext(ctx, `SELECT COUNT(*) FROM spans`)
	if err := row.Scan(&stats.SpanCount); err != nil {
		return nil, fmt.Errorf("failed to count spans: %w", err)
	}
	
	// Count services
	row = s.db.QueryRowContext(ctx, `SELECT COUNT(DISTINCT service_name) FROM processes`)
	if err := row.Scan(&stats.ServiceCount); err != nil {
		return nil, fmt.Errorf("failed to count services: %w", err)
	}
	
	// Count operations
	row = s.db.QueryRowContext(ctx, `SELECT COUNT(DISTINCT operation_name) FROM spans`)
	if err := row.Scan(&stats.OperationCount); err != nil {
		return nil, fmt.Errorf("failed to count operations: %w", err)
	}
	
	// Get oldest and newest traces
	row = s.db.QueryRowContext(ctx, `SELECT MIN(start_time), MAX(start_time) FROM traces`)
	var oldestTime, newestTime sql.NullInt64
	if err := row.Scan(&oldestTime, &newestTime); err != nil {
		return nil, fmt.Errorf("failed to get trace time range: %w", err)
	}
	
	if oldestTime.Valid {
		stats.OldestTrace = time.UnixMicro(oldestTime.Int64)
	}
	if newestTime.Valid {
		stats.NewestTrace = time.UnixMicro(newestTime.Int64)
	}
	
	// Get storage size (simplified - just count rows)
	stats.StorageSize = stats.TraceCount + stats.SpanCount
	
	return &stats, nil
}

// buildTraceQuery builds a SQL query for finding traces
func (s *SQLiteStorage) buildTraceQuery(query *storage.TraceQuery) (string, []interface{}) {
	var conditions []string
	var args []interface{}

	baseQuery := `
		SELECT t.trace_id, t.start_time, t.end_time, t.duration,
		       t.service_count, t.span_count, t.error_count, t.tags, t.warnings
		FROM traces t
	`

	// Add service filter
	if query.Service != "" {
		baseQuery += ` JOIN processes p ON t.trace_id = p.trace_id`
		conditions = append(conditions, "p.service_name = ?")
		args = append(args, query.Service)
	}

	// Add operation filter
	if query.Operation != "" {
		if query.Service == "" {
			baseQuery += ` JOIN spans s ON t.trace_id = s.trace_id`
		} else {
			baseQuery += ` JOIN spans s ON t.trace_id = s.trace_id AND s.process_id = p.process_id`
		}
		conditions = append(conditions, "s.operation_name = ?")
		args = append(args, query.Operation)
	}

	// Add time range filters
	if !query.StartTime.IsZero() {
		conditions = append(conditions, "t.start_time >= ?")
		args = append(args, query.StartTime.UnixMicro())
	}
	if !query.EndTime.IsZero() {
		conditions = append(conditions, "t.start_time <= ?")
		args = append(args, query.EndTime.UnixMicro())
	}

	// Add duration filters
	if query.MinDuration > 0 {
		conditions = append(conditions, "t.duration >= ?")
		args = append(args, query.MinDuration)
	}
	if query.MaxDuration > 0 {
		conditions = append(conditions, "t.duration <= ?")
		args = append(args, query.MaxDuration)
	}

	// Add error filter
	if query.HasError != nil {
		if *query.HasError {
			conditions = append(conditions, "t.error_count > 0")
		} else {
			conditions = append(conditions, "t.error_count = 0")
		}
	}

	// Build WHERE clause
	if len(conditions) > 0 {
		baseQuery += " WHERE " + strings.Join(conditions, " AND ")
	}

	// Add GROUP BY if we joined with other tables
	if query.Service != "" || query.Operation != "" {
		baseQuery += " GROUP BY t.trace_id"
	}

	// Add ORDER BY
	orderBy := "t.start_time DESC"
	if query.SortBy != "" {
		switch query.SortBy {
		case "startTime":
			orderBy = "t.start_time"
		case "duration":
			orderBy = "t.duration"
		case "service":
			if query.Service == "" {
				orderBy = "t.start_time"
			}
		}

		if query.SortOrder == "asc" {
			orderBy += " ASC"
		} else {
			orderBy += " DESC"
		}
	}
	baseQuery += " ORDER BY " + orderBy

	// Add LIMIT and OFFSET
	if query.Limit > 0 {
		baseQuery += " LIMIT ?"
		args = append(args, query.Limit)

		if query.Offset > 0 {
			baseQuery += " OFFSET ?"
			args = append(args, query.Offset)
		}
	}

	return baseQuery, args
}

// buildSpanQuery builds a SQL query for finding spans
func (s *SQLiteStorage) buildSpanQuery(query *storage.SpanQuery) (string, []interface{}) {
	var conditions []string
	var args []interface{}

	baseQuery := `
		SELECT s.trace_id, s.span_id, s.parent_span_id, s.operation_name, s.start_time, s.duration,
		       s.process_id, s.tags, s.logs, s.references, s.warnings, s.kind, s.status_code, s.status_message
		FROM spans s
	`

	// Add trace ID filter
	if query.TraceID != "" {
		conditions = append(conditions, "s.trace_id = ?")
		args = append(args, query.TraceID)
	}

	// Add service filter
	if query.Service != "" {
		baseQuery += ` JOIN processes p ON s.trace_id = p.trace_id AND s.process_id = p.process_id`
		conditions = append(conditions, "p.service_name = ?")
		args = append(args, query.Service)
	}

	// Add operation filter
	if query.Operation != "" {
		conditions = append(conditions, "s.operation_name = ?")
		args = append(args, query.Operation)
	}

	// Add time range filters
	if !query.StartTime.IsZero() {
		conditions = append(conditions, "s.start_time >= ?")
		args = append(args, query.StartTime.UnixMicro())
	}
	if !query.EndTime.IsZero() {
		conditions = append(conditions, "s.start_time <= ?")
		args = append(args, query.EndTime.UnixMicro())
	}

	// Add duration filters
	if query.MinDuration > 0 {
		conditions = append(conditions, "s.duration >= ?")
		args = append(args, query.MinDuration)
	}
	if query.MaxDuration > 0 {
		conditions = append(conditions, "s.duration <= ?")
		args = append(args, query.MaxDuration)
	}

	// Add error filter
	if query.HasError != nil {
		if *query.HasError {
			conditions = append(conditions, "(s.status_code >= 400 OR s.tags LIKE '%\"error\":true%')")
		} else {
			conditions = append(conditions, "(s.status_code < 400 AND s.tags NOT LIKE '%\"error\":true%')")
		}
	}

	// Build WHERE clause
	if len(conditions) > 0 {
		baseQuery += " WHERE " + strings.Join(conditions, " AND ")
	}

	// Add ORDER BY
	baseQuery += " ORDER BY s.start_time ASC"

	// Add LIMIT and OFFSET
	if query.Limit > 0 {
		baseQuery += " LIMIT ?"
		args = append(args, query.Limit)

		if query.Offset > 0 {
			baseQuery += " OFFSET ?"
			args = append(args, query.Offset)
		}
	}

	return baseQuery, args
}

// scanTraceMetadata scans trace metadata from a database row
func (s *SQLiteStorage) scanTraceMetadata(scanner interface {
	Scan(dest ...interface{}) error
}) (*models.Trace, error) {
	var trace models.Trace
	var startTime, endTime int64
	var tagsJSON, warningsJSON string

	err := scanner.Scan(
		&trace.TraceID,
		&startTime,
		&endTime,
		&trace.Duration,
		&trace.ServiceCount,
		&trace.SpanCount,
		&trace.ErrorCount,
		&tagsJSON,
		&warningsJSON,
	)

	if err != nil {
		return nil, err
	}

	// Convert timestamps
	trace.StartTime = time.UnixMicro(startTime)
	trace.EndTime = time.UnixMicro(endTime)

	// Parse JSON fields
	if tagsJSON != "" {
		json.Unmarshal([]byte(tagsJSON), &trace.Tags)
	}
	if warningsJSON != "" {
		json.Unmarshal([]byte(warningsJSON), &trace.Warnings)
	}

	return &trace, nil
}
