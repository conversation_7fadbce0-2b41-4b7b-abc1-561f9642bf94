package models

import (
	"encoding/json"
	"time"
)

// TraceID represents a unique trace identifier
type Trace<PERSON> string

// SpanID represents a unique span identifier
type SpanID string

// Trace represents a complete trace with all its spans
type Trace struct {
	TraceID   TraceID           `json:"traceID" db:"trace_id"`
	Spans     []Span            `json:"spans" db:"-"`
	Processes map[string]Process `json:"processes" db:"-"`
	Warnings  []string          `json:"warnings,omitempty" db:"-"`
	
	// Metadata
	StartTime time.Time `json:"startTime" db:"start_time"`
	EndTime   time.Time `json:"endTime" db:"end_time"`
	Duration  int64     `json:"duration" db:"duration"` // microseconds
	
	// Aggregated information
	ServiceCount   int `json:"serviceCount" db:"service_count"`
	SpanCount      int `json:"spanCount" db:"span_count"`
	ErrorCount     int `json:"errorCount" db:"error_count"`
	
	// Tags for filtering and searching
	Tags map[string]interface{} `json:"tags,omitempty" db:"tags"`
}

// Span represents a single span in a trace
type Span struct {
	TraceID       TraceID                `json:"traceID" db:"trace_id"`
	SpanID        SpanID                 `json:"spanID" db:"span_id"`
	ParentSpanID  SpanID                 `json:"parentSpanID,omitempty" db:"parent_span_id"`
	OperationName string                 `json:"operationName" db:"operation_name"`
	
	// Timing information
	StartTime int64 `json:"startTime" db:"start_time"` // microseconds since epoch
	Duration  int64 `json:"duration" db:"duration"`    // microseconds
	
	// Process information
	ProcessID string `json:"processID" db:"process_id"`
	
	// Tags and logs
	Tags []KeyValue `json:"tags,omitempty" db:"tags"`
	Logs []Log      `json:"logs,omitempty" db:"logs"`
	
	// References to other spans
	References []SpanRef `json:"references,omitempty" db:"references"`
	
	// Warnings
	Warnings []string `json:"warnings,omitempty" db:"warnings"`
	
	// Additional metadata
	Kind       string `json:"kind,omitempty" db:"kind"`           // client, server, producer, consumer, internal
	StatusCode int32  `json:"statusCode,omitempty" db:"status_code"`
	StatusMessage string `json:"statusMessage,omitempty" db:"status_message"`
}

// Process represents a process that generated spans
type Process struct {
	ServiceName string     `json:"serviceName" db:"service_name"`
	Tags        []KeyValue `json:"tags,omitempty" db:"tags"`
}

// KeyValue represents a key-value pair for tags
type KeyValue struct {
	Key   string      `json:"key" db:"key"`
	Value interface{} `json:"value" db:"value"`
	Type  string      `json:"type,omitempty" db:"type"` // string, bool, int64, float64, binary
}

// Log represents a log entry within a span
type Log struct {
	Timestamp int64      `json:"timestamp" db:"timestamp"` // microseconds since epoch
	Fields    []KeyValue `json:"fields" db:"fields"`
}

// SpanRef represents a reference from one span to another
type SpanRef struct {
	RefType string  `json:"refType" db:"ref_type"` // CHILD_OF, FOLLOWS_FROM
	TraceID TraceID `json:"traceID" db:"trace_id"`
	SpanID  SpanID  `json:"spanID" db:"span_id"`
}

// SpanRefType constants
const (
	ChildOf     = "CHILD_OF"
	FollowsFrom = "FOLLOWS_FROM"
)

// SpanKind constants
const (
	SpanKindUnspecified = "unspecified"
	SpanKindInternal    = "internal"
	SpanKindServer      = "server"
	SpanKindClient      = "client"
	SpanKindProducer    = "producer"
	SpanKindConsumer    = "consumer"
)

// GetEndTime returns the end time of the span
func (s *Span) GetEndTime() int64 {
	return s.StartTime + s.Duration
}

// HasError checks if the span has an error
func (s *Span) HasError() bool {
	for _, tag := range s.Tags {
		if tag.Key == "error" {
			if val, ok := tag.Value.(bool); ok && val {
				return true
			}
		}
	}
	return s.StatusCode != 0 && s.StatusCode >= 400
}

// GetTag returns the value of a tag by key
func (s *Span) GetTag(key string) (interface{}, bool) {
	for _, tag := range s.Tags {
		if tag.Key == key {
			return tag.Value, true
		}
	}
	return nil, false
}

// AddTag adds a tag to the span
func (s *Span) AddTag(key string, value interface{}) {
	s.Tags = append(s.Tags, KeyValue{
		Key:   key,
		Value: value,
		Type:  getValueType(value),
	})
}

// AddLog adds a log entry to the span
func (s *Span) AddLog(timestamp int64, fields map[string]interface{}) {
	var logFields []KeyValue
	for k, v := range fields {
		logFields = append(logFields, KeyValue{
			Key:   k,
			Value: v,
			Type:  getValueType(v),
		})
	}
	
	s.Logs = append(s.Logs, Log{
		Timestamp: timestamp,
		Fields:    logFields,
	})
}

// getValueType determines the type of a value
func getValueType(value interface{}) string {
	switch value.(type) {
	case string:
		return "string"
	case bool:
		return "bool"
	case int, int8, int16, int32, int64:
		return "int64"
	case uint, uint8, uint16, uint32, uint64:
		return "int64"
	case float32, float64:
		return "float64"
	case []byte:
		return "binary"
	default:
		return "string"
	}
}

// MarshalJSON implements custom JSON marshaling for KeyValue
func (kv KeyValue) MarshalJSON() ([]byte, error) {
	return json.Marshal(map[string]interface{}{
		"key":   kv.Key,
		"value": kv.Value,
		"type":  kv.Type,
	})
}

// UnmarshalJSON implements custom JSON unmarshaling for KeyValue
func (kv *KeyValue) UnmarshalJSON(data []byte) error {
	var temp map[string]interface{}
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}
	
	kv.Key = temp["key"].(string)
	kv.Value = temp["value"]
	if t, ok := temp["type"]; ok {
		kv.Type = t.(string)
	} else {
		kv.Type = getValueType(kv.Value)
	}
	
	return nil
}
