package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"distributed-tracing-tool/internal/models"
	"distributed-tracing-tool/pkg/logger"

	"github.com/gin-gonic/gin"
)

// HTTPCollector handles HTTP-based trace data collection
type HTTPCollector struct {
	logger    *logger.Logger
	processor *Processor
	config    *HTTPCollectorConfig
}

// HTTPCollectorConfig holds configuration for HTTP collector
type HTTPCollectorConfig struct {
	MaxRequestSize int           `json:"maxRequestSize"`
	Timeout        time.Duration `json:"timeout"`
	RateLimit      int           `json:"rateLimit"` // requests per second
}

// DefaultHTTPCollectorConfig returns default HTTP collector configuration
func DefaultHTTPCollectorConfig() *HTTPCollectorConfig {
	return &HTTPCollectorConfig{
		MaxRequestSize: 10 * 1024 * 1024, // 10MB
		Timeout:        30 * time.Second,
		RateLimit:      1000, // 1000 requests per second
	}
}

// NewHTTPCollector creates a new HTTP collector
func NewHTTPCollector(logger *logger.Logger, processor *Processor, config *HTTPCollectorConfig) *HTTPCollector {
	if config == nil {
		config = DefaultHTTPCollectorConfig()
	}
	
	return &HTTPCollector{
		logger:    logger,
		processor: processor,
		config:    config,
	}
}

// RegisterRoutes registers HTTP collector routes
func (c *HTTPCollector) RegisterRoutes(router *gin.Engine) {
	collector := router.Group("/api/collector")
	{
		// OpenTelemetry compatible endpoints
		collector.POST("/v1/traces", c.handleTraces)
		collector.POST("/v1/spans", c.handleSpans)
		
		// Jaeger compatible endpoints
		collector.POST("/api/traces", c.handleJaegerTraces)
		
		// Custom endpoints
		collector.POST("/traces", c.handleTraces)
		collector.POST("/spans", c.handleSpans)
		collector.POST("/batch", c.handleBatch)
	}
}

// handleTraces handles trace submission
func (c *HTTPCollector) handleTraces(ctx *gin.Context) {
	// Check content type
	contentType := ctx.GetHeader("Content-Type")
	if contentType != "application/json" && contentType != "application/x-protobuf" {
		ctx.JSON(http.StatusUnsupportedMediaType, gin.H{
			"error": "Unsupported content type. Expected application/json or application/x-protobuf",
		})
		return
	}
	
	// Read request body
	body, err := ctx.GetRawData()
	if err != nil {
		c.logger.WithError(err).Error("Failed to read request body")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to read request body",
		})
		return
	}
	
	// Check request size
	if len(body) > c.config.MaxRequestSize {
		ctx.JSON(http.StatusRequestEntityTooLarge, gin.H{
			"error": fmt.Sprintf("Request too large. Maximum size: %d bytes", c.config.MaxRequestSize),
		})
		return
	}
	
	// Parse traces based on content type
	var traces []*models.Trace
	if contentType == "application/json" {
		traces, err = c.parseJSONTraces(body)
	} else {
		// TODO: Implement protobuf parsing
		ctx.JSON(http.StatusNotImplemented, gin.H{
			"error": "Protobuf parsing not yet implemented",
		})
		return
	}
	
	if err != nil {
		c.logger.WithError(err).Error("Failed to parse traces")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to parse traces: " + err.Error(),
		})
		return
	}
	
	// Process traces
	processed := 0
	failed := 0
	
	for _, trace := range traces {
		if err := c.processor.ProcessTrace(ctx, trace); err != nil {
			c.logger.WithError(err).WithField("trace_id", trace.TraceID).Error("Failed to process trace")
			failed++
		} else {
			processed++
		}
	}
	
	c.logger.WithFields(map[string]interface{}{
		"processed": processed,
		"failed":    failed,
		"total":     len(traces),
	}).Info("Processed trace batch")
	
	// Return response
	ctx.JSON(http.StatusOK, gin.H{
		"processed": processed,
		"failed":    failed,
		"total":     len(traces),
	})
}

// handleSpans handles span submission
func (c *HTTPCollector) handleSpans(ctx *gin.Context) {
	// Read request body
	body, err := ctx.GetRawData()
	if err != nil {
		c.logger.WithError(err).Error("Failed to read request body")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to read request body",
		})
		return
	}
	
	// Parse spans
	var spans []*models.Span
	if err := json.Unmarshal(body, &spans); err != nil {
		// Try parsing as single span
		var span models.Span
		if err := json.Unmarshal(body, &span); err != nil {
			c.logger.WithError(err).Error("Failed to parse spans")
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Failed to parse spans: " + err.Error(),
			})
			return
		}
		spans = []*models.Span{&span}
	}
	
	// Process spans
	processed := 0
	failed := 0
	
	for _, span := range spans {
		if err := c.processor.ProcessSpan(ctx, span); err != nil {
			c.logger.WithError(err).WithField("span_id", span.SpanID).Error("Failed to process span")
			failed++
		} else {
			processed++
		}
	}
	
	c.logger.WithFields(map[string]interface{}{
		"processed": processed,
		"failed":    failed,
		"total":     len(spans),
	}).Info("Processed span batch")
	
	// Return response
	ctx.JSON(http.StatusOK, gin.H{
		"processed": processed,
		"failed":    failed,
		"total":     len(spans),
	})
}

// handleBatch handles batch submission of mixed trace data
func (c *HTTPCollector) handleBatch(ctx *gin.Context) {
	// Read request body
	body, err := ctx.GetRawData()
	if err != nil {
		c.logger.WithError(err).Error("Failed to read request body")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to read request body",
		})
		return
	}
	
	// Parse batch data
	var batch struct {
		Traces []*models.Trace `json:"traces,omitempty"`
		Spans  []*models.Span  `json:"spans,omitempty"`
	}
	
	if err := json.Unmarshal(body, &batch); err != nil {
		c.logger.WithError(err).Error("Failed to parse batch data")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to parse batch data: " + err.Error(),
		})
		return
	}
	
	// Process traces and spans
	processedTraces := 0
	failedTraces := 0
	processedSpans := 0
	failedSpans := 0
	
	// Process traces
	for _, trace := range batch.Traces {
		if err := c.processor.ProcessTrace(ctx, trace); err != nil {
			c.logger.WithError(err).WithField("trace_id", trace.TraceID).Error("Failed to process trace")
			failedTraces++
		} else {
			processedTraces++
		}
	}
	
	// Process spans
	for _, span := range batch.Spans {
		if err := c.processor.ProcessSpan(ctx, span); err != nil {
			c.logger.WithError(err).WithField("span_id", span.SpanID).Error("Failed to process span")
			failedSpans++
		} else {
			processedSpans++
		}
	}
	
	c.logger.WithFields(map[string]interface{}{
		"processed_traces": processedTraces,
		"failed_traces":    failedTraces,
		"processed_spans":  processedSpans,
		"failed_spans":     failedSpans,
	}).Info("Processed batch")
	
	// Return response
	ctx.JSON(http.StatusOK, gin.H{
		"traces": gin.H{
			"processed": processedTraces,
			"failed":    failedTraces,
			"total":     len(batch.Traces),
		},
		"spans": gin.H{
			"processed": processedSpans,
			"failed":    failedSpans,
			"total":     len(batch.Spans),
		},
	})
}

// handleJaegerTraces handles Jaeger-compatible trace submission
func (c *HTTPCollector) handleJaegerTraces(ctx *gin.Context) {
	// TODO: Implement Jaeger format parsing
	ctx.JSON(http.StatusNotImplemented, gin.H{
		"error": "Jaeger format not yet implemented",
	})
}

// parseJSONTraces parses JSON traces from request body
func (c *HTTPCollector) parseJSONTraces(body []byte) ([]*models.Trace, error) {
	var traces []*models.Trace
	
	// Try parsing as array of traces
	if err := json.Unmarshal(body, &traces); err != nil {
		// Try parsing as single trace
		var trace models.Trace
		if err := json.Unmarshal(body, &trace); err != nil {
			return nil, fmt.Errorf("failed to parse as trace or trace array: %w", err)
		}
		traces = []*models.Trace{&trace}
	}
	
	return traces, nil
}

// rateLimitMiddleware provides rate limiting for collector endpoints
func (c *HTTPCollector) rateLimitMiddleware() gin.HandlerFunc {
	// Simple in-memory rate limiter
	// In production, use Redis or similar for distributed rate limiting
	requests := make(map[string][]time.Time)
	
	return func(ctx *gin.Context) {
		clientIP := ctx.ClientIP()
		now := time.Now()
		
		// Clean old requests
		if reqs, exists := requests[clientIP]; exists {
			var validReqs []time.Time
			for _, reqTime := range reqs {
				if now.Sub(reqTime) < time.Second {
					validReqs = append(validReqs, reqTime)
				}
			}
			requests[clientIP] = validReqs
		}
		
		// Check rate limit
		if len(requests[clientIP]) >= c.config.RateLimit {
			ctx.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"limit": c.config.RateLimit,
			})
			ctx.Abort()
			return
		}
		
		// Add current request
		requests[clientIP] = append(requests[clientIP], now)
		
		ctx.Next()
	}
}

// compressionMiddleware provides response compression
func (c *HTTPCollector) compressionMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Check if client accepts gzip
		if ctx.GetHeader("Accept-Encoding") != "" {
			ctx.Header("Content-Encoding", "gzip")
		}
		ctx.Next()
	}
}

// metricsMiddleware provides metrics collection
func (c *HTTPCollector) metricsMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		start := time.Now()
		
		ctx.Next()
		
		duration := time.Since(start)
		
		c.logger.WithFields(map[string]interface{}{
			"method":     ctx.Request.Method,
			"path":       ctx.Request.URL.Path,
			"status":     ctx.Writer.Status(),
			"duration":   duration,
			"size":       ctx.Request.ContentLength,
			"user_agent": ctx.Request.UserAgent(),
		}).Info("HTTP collector request")
	}
}
