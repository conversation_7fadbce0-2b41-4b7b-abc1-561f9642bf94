# Multi-stage build for distributed tracing tool

# Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git make gcc musl-dev sqlite-dev

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -ldflags "-X main.version=docker -X main.commit=$(git rev-parse --short HEAD 2>/dev/null || echo unknown) -X main.date=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o bin/distributed-tracing-tool .

# Runtime stage
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache ca-certificates sqlite

# Create non-root user
RUN addgroup -g 1001 -S tracing && \
    adduser -u 1001 -S tracing -G tracing

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/bin/distributed-tracing-tool .

# Copy configuration file
COPY config.yaml .

# Create data directory
RUN mkdir -p data && chown -R tracing:tracing /app

# Switch to non-root user
USER tracing

# Expose ports
EXPOSE 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./distributed-tracing-tool", "--config", "config.yaml"]
