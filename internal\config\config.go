package config

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Server ServerConfig `mapstructure:"server"`
	
	// Storage configuration
	Storage StorageConfig `mapstructure:"storage"`
	
	// Collector configuration
	Collector CollectorConfig `mapstructure:"collector"`
	
	// Query configuration
	Query QueryConfig `mapstructure:"query"`
	
	// Web UI configuration
	WebUI WebUIConfig `mapstructure:"webui"`
	
	// Logging configuration
	LogLevel string `mapstructure:"log_level"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	HTTPPort int    `mapstructure:"http_port"`
	GRPCPort int    `mapstructure:"grpc_port"`
	Host     string `mapstructure:"host"`
}

// StorageConfig holds storage backend configuration
type StorageConfig struct {
	Backend string `mapstructure:"backend"`
	DSN     string `mapstructure:"dsn"`
	
	// SQLite specific
	SQLite SQLiteConfig `mapstructure:"sqlite"`
	
	// ClickHouse specific
	ClickHouse ClickHouseConfig `mapstructure:"clickhouse"`
	
	// PostgreSQL specific
	PostgreSQL PostgreSQLConfig `mapstructure:"postgresql"`
}

// SQLiteConfig holds SQLite-specific configuration
type SQLiteConfig struct {
	Path           string `mapstructure:"path"`
	MaxConnections int    `mapstructure:"max_connections"`
}

// ClickHouseConfig holds ClickHouse-specific configuration
type ClickHouseConfig struct {
	Addresses []string `mapstructure:"addresses"`
	Database  string   `mapstructure:"database"`
	Username  string   `mapstructure:"username"`
	Password  string   `mapstructure:"password"`
}

// PostgreSQLConfig holds PostgreSQL-specific configuration
type PostgreSQLConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Database string `mapstructure:"database"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	SSLMode  string `mapstructure:"ssl_mode"`
}

// CollectorConfig holds data collector configuration
type CollectorConfig struct {
	BatchSize    int `mapstructure:"batch_size"`
	BatchTimeout int `mapstructure:"batch_timeout"`
	QueueSize    int `mapstructure:"queue_size"`
}

// QueryConfig holds query service configuration
type QueryConfig struct {
	MaxResults      int `mapstructure:"max_results"`
	DefaultPageSize int `mapstructure:"default_page_size"`
	CacheEnabled    bool `mapstructure:"cache_enabled"`
	CacheTTL        int  `mapstructure:"cache_ttl"`
}

// WebUIConfig holds web UI configuration
type WebUIConfig struct {
	Enabled    bool   `mapstructure:"enabled"`
	StaticPath string `mapstructure:"static_path"`
}

// Load loads configuration from file and command line flags
func Load(cmd *cobra.Command) (*Config, error) {
	v := viper.New()
	
	// Set default values
	setDefaults(v)
	
	// Read config file if specified
	if configFile, _ := cmd.Flags().GetString("config"); configFile != "" {
		v.SetConfigFile(configFile)
		if err := v.ReadInConfig(); err != nil {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}
	
	// Bind command line flags
	if err := bindFlags(cmd, v); err != nil {
		return nil, fmt.Errorf("failed to bind flags: %w", err)
	}
	
	// Enable environment variable support
	v.SetEnvPrefix("TRACING")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()
	
	// Unmarshal configuration
	var cfg Config
	if err := v.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	
	return &cfg, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// Server defaults
	v.SetDefault("server.http_port", 8080)
	v.SetDefault("server.grpc_port", 9090)
	v.SetDefault("server.host", "0.0.0.0")
	
	// Storage defaults
	v.SetDefault("storage.backend", "sqlite")
	v.SetDefault("storage.dsn", "./traces.db")
	v.SetDefault("storage.sqlite.path", "./traces.db")
	v.SetDefault("storage.sqlite.max_connections", 10)
	
	// Collector defaults
	v.SetDefault("collector.batch_size", 1000)
	v.SetDefault("collector.batch_timeout", 5)
	v.SetDefault("collector.queue_size", 10000)
	
	// Query defaults
	v.SetDefault("query.max_results", 10000)
	v.SetDefault("query.default_page_size", 100)
	v.SetDefault("query.cache_enabled", true)
	v.SetDefault("query.cache_ttl", 300)
	
	// WebUI defaults
	v.SetDefault("webui.enabled", true)
	v.SetDefault("webui.static_path", "./web/dist")
	
	// Logging defaults
	v.SetDefault("log_level", "info")
}

// bindFlags binds command line flags to viper
func bindFlags(cmd *cobra.Command, v *viper.Viper) error {
	flags := map[string]string{
		"port":        "server.http_port",
		"grpc-port":   "server.grpc_port",
		"storage":     "storage.backend",
		"storage-dsn": "storage.dsn",
		"log-level":   "log_level",
	}
	
	for flag, key := range flags {
		if err := v.BindPFlag(key, cmd.Flags().Lookup(flag)); err != nil {
			return err
		}
	}
	
	return nil
}
