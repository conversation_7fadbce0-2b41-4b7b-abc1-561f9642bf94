<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分布式链路追踪工具 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px 24px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4aa, #7bed9f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            margin: 0 24px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #00d4aa;
            box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b7a, #ffa726);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* 标签页导航 */
        .tab-nav {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
            background: rgba(255, 255, 255, 0.03);
            padding: 8px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab-btn {
            padding: 12px 24px;
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #00d4aa, #7bed9f);
            color: white;
            box-shadow: 0 4px 20px rgba(0, 212, 170, 0.3);
        }

        .tab-btn:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* 内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-value {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .stat-trend {
            margin-top: 8px;
            font-size: 12px;
        }

        .trend-up {
            color: #7bed9f;
        }

        .trend-down {
            color: #ff6b7a;
        }

        /* 链路时间线 */
        .timeline-container {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .trace-info {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .service-lane {
            background: rgba(255, 255, 255, 0.03);
            border-left: 4px solid var(--service-color);
            margin: 12px 0;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .service-lane:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateX(4px);
        }

        .service-name {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .service-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--service-color);
        }

        .span-container {
            position: relative;
            height: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-top: 8px;
        }

        .span-block {
            position: absolute;
            height: 32px;
            top: 4px;
            background: linear-gradient(135deg, var(--span-color), var(--span-color-light));
            border-radius: 6px;
            display: flex;
            align-items: center;
            padding: 0 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .span-block:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            z-index: 10;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .navbar {
                flex-direction: column;
                gap: 16px;
            }
            
            .search-box {
                margin: 0;
                max-width: none;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .tab-nav {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <div class="logo">🔍 Distributed Tracing Tool</div>
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索 trace-id 或服务名称...">
            </div>
            <div class="user-info">
                <span>⚙️</span>
                <div class="user-avatar">U</div>
            </div>
        </nav>

        <!-- 标签页导航 -->
        <div class="tab-nav">
            <button class="tab-btn active" onclick="switchTab('timeline')">🕐 链路追踪</button>
            <button class="tab-btn" onclick="switchTab('servicemap')">🗺️ 服务地图</button>
            <button class="tab-btn" onclick="switchTab('performance')">📊 性能监控</button>
            <button class="tab-btn" onclick="switchTab('diagnosis')">🔍 问题诊断</button>
            <button class="tab-btn" onclick="switchTab('resources')">🖥️ 系统资源</button>
        </div>

        <!-- 链路追踪时间线 -->
        <div id="timeline" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" style="color: #00d4aa;">1,247</div>
                    <div class="stat-label">请求/秒</div>
                    <div class="stat-trend trend-up">↗️ +15%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #ffa726;">324ms</div>
                    <div class="stat-label">平均延迟</div>
                    <div class="stat-trend trend-up">↗️ +15ms</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #ff6b7a;">2.3%</div>
                    <div class="stat-label">错误率</div>
                    <div class="stat-trend trend-up">↗️ +0.8%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #7bed9f;">0.85</div>
                    <div class="stat-label">Apdex</div>
                    <div class="stat-trend trend-down">↘️ -0.05</div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    🕐 链路时间线视图
                    <span style="font-size: 14px; color: rgba(255,255,255,0.7);">Trace: abc123def456 | Duration: 245ms | Services: 4 | Spans: 12 | ❌ 2 Errors</span>
                </div>
                
                <div class="timeline-container">
                    <div class="timeline-header">
                        <div class="trace-info">时间轴: 0ms ────────── 100ms ────────── 200ms ──────── 245ms</div>
                    </div>
                    
                    <div class="service-lane" style="--service-color: #00d4aa;">
                        <div class="service-name">
                            <div class="service-icon"></div>
                            🟦 user-service
                        </div>
                        <div class="span-container">
                            <div class="span-block" style="--span-color: #00d4aa; --span-color-light: #7bed9f; left: 0%; width: 100%;">
                                POST /api/user/login [245ms]
                            </div>
                        </div>
                    </div>
                    
                    <div class="service-lane" style="--service-color: #7bed9f;">
                        <div class="service-name">
                            <div class="service-icon"></div>
                            🟢 auth-service
                        </div>
                        <div class="span-container">
                            <div class="span-block" style="--span-color: #7bed9f; --span-color-light: #00d4aa; left: 10%; width: 35%;">
                                validateCredentials [85ms]
                            </div>
                        </div>
                    </div>
                    
                    <div class="service-lane" style="--service-color: #ffa726;">
                        <div class="service-name">
                            <div class="service-icon"></div>
                            🟡 database-service
                        </div>
                        <div class="span-container">
                            <div class="span-block" style="--span-color: #ffa726; --span-color-light: #ffcc02; left: 15%; width: 18%;">
                                SELECT * FROM users [45ms]
                            </div>
                        </div>
                    </div>
                    
                    <div class="service-lane" style="--service-color: #ff6b7a;">
                        <div class="service-name">
                            <div class="service-icon"></div>
                            🔴 cache-service
                        </div>
                        <div class="span-container">
                            <div class="span-block" style="--span-color: #ff6b7a; --span-color-light: #ff4757; left: 5%; width: 73%;">
                                redis.get('user:123') ❌ TIMEOUT [180ms]
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card" style="margin-top: 20px;">
                    <div class="card-title">📊 快速统计</div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div>• 总耗时: 245ms</div>
                        <div>• 关键路径: user-service → cache-service</div>
                        <div>• 瓶颈: cache-service (73% 总时间)</div>
                        <div>• 错误率: 16.7% (2/12 spans)</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有按钮的激活状态
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的按钮
            event.target.classList.add('active');
        }

        // 模拟实时数据更新
        function updateStats() {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(stat => {
                const currentValue = parseFloat(stat.textContent);
                if (!isNaN(currentValue)) {
                    const variation = (Math.random() - 0.5) * 0.1;
                    const newValue = currentValue * (1 + variation);
                    stat.textContent = newValue.toFixed(stat.textContent.includes('.') ? 2 : 0);
                }
            });
        }

        // 每5秒更新一次数据
        setInterval(updateStats, 5000);

        // 添加搜索框焦点效果
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('focus', function() {
            this.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.style.transform = 'scale(1)';
        });
    </script>
</body>
</html>
