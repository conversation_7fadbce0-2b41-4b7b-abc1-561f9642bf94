# Distributed Tracing Tool Configuration

# Server configuration
server:
  http_port: 8080
  grpc_port: 9090
  host: "0.0.0.0"

# Storage configuration
storage:
  backend: "sqlite"  # Options: sqlite, clickhouse, postgresql
  dsn: "./traces.db"
  
  # SQLite specific configuration
  sqlite:
    path: "./traces.db"
    max_connections: 10
  
  # ClickHouse specific configuration (for future use)
  clickhouse:
    addresses: ["localhost:9000"]
    database: "tracing"
    username: "default"
    password: ""
  
  # PostgreSQL specific configuration (for future use)
  postgresql:
    host: "localhost"
    port: 5432
    database: "tracing"
    username: "postgres"
    password: ""
    ssl_mode: "disable"

# Data collector configuration
collector:
  batch_size: 1000
  batch_timeout: 5  # seconds
  queue_size: 10000

# Query service configuration
query:
  max_results: 10000
  default_page_size: 100
  cache_enabled: true
  cache_ttl: 300  # seconds

# Web UI configuration
webui:
  enabled: true
  static_path: "./web"

# Logging configuration
log_level: "info"  # Options: debug, info, warn, error
