package query

import (
	"net/http"
	"strconv"
	"time"

	"distributed-tracing-tool/internal/models"
	"distributed-tracing-tool/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Handlers provides HTTP handlers for query operations
type Handlers struct {
	service *Service
	logger  *logger.Logger
}

// NewHandlers creates new query handlers
func NewHandlers(service *Service, logger *logger.Logger) *Handlers {
	return &Handlers{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers query API routes
func (h *Handlers) RegisterRoutes(router *gin.RouterGroup) {
	// Trace endpoints
	router.GET("/traces", h.findTraces)
	router.GET("/traces/:traceId", h.getTrace)
	router.GET("/traces/:traceId/stats", h.getTraceStats)
	
	// Service endpoints
	router.GET("/services", h.getServices)
	router.GET("/services/:service/operations", h.getOperations)
	router.GET("/services/:service/stats", h.getServiceStats)
	
	// System endpoints
	router.GET("/stats", h.getStorageStats)
}

// findTraces handles trace search requests
func (h *Handlers) findTraces(c *gin.Context) {
	// Parse query parameters
	params, err := h.parseTraceQueryParams(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to parse trace query parameters")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid query parameters: " + err.Error(),
		})
		return
	}
	
	// Execute search
	result, err := h.service.FindTraces(c.Request.Context(), params)
	if err != nil {
		h.logger.WithError(err).Error("Failed to find traces")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to search traces: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, result)
}

// getTrace handles single trace retrieval
func (h *Handlers) getTrace(c *gin.Context) {
	traceID := c.Param("traceId")
	if traceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Trace ID is required",
		})
		return
	}
	
	trace, err := h.service.GetTrace(c.Request.Context(), traceID)
	if err != nil {
		h.logger.WithError(err).WithField("trace_id", traceID).Error("Failed to get trace")
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Trace not found: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, trace)
}

// getTraceStats handles trace statistics retrieval
func (h *Handlers) getTraceStats(c *gin.Context) {
	traceID := c.Param("traceId")
	if traceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Trace ID is required",
		})
		return
	}
	
	stats, err := h.service.GetTraceStats(c.Request.Context(), traceID)
	if err != nil {
		h.logger.WithError(err).WithField("trace_id", traceID).Error("Failed to get trace stats")
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Trace stats not found: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, stats)
}

// getServices handles service list retrieval
func (h *Handlers) getServices(c *gin.Context) {
	services, err := h.service.GetServices(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get services")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get services: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"services": services,
	})
}

// getOperations handles operation list retrieval for a service
func (h *Handlers) getOperations(c *gin.Context) {
	service := c.Param("service")
	if service == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Service name is required",
		})
		return
	}
	
	operations, err := h.service.GetOperations(c.Request.Context(), service)
	if err != nil {
		h.logger.WithError(err).WithField("service", service).Error("Failed to get operations")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get operations: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"service":    service,
		"operations": operations,
	})
}

// getServiceStats handles service statistics retrieval
func (h *Handlers) getServiceStats(c *gin.Context) {
	service := c.Param("service")
	if service == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Service name is required",
		})
		return
	}
	
	// Parse time range parameters
	timeRange, err := h.parseTimeRange(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to parse time range")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid time range: " + err.Error(),
		})
		return
	}
	
	stats, err := h.service.GetServiceStats(c.Request.Context(), service, timeRange)
	if err != nil {
		h.logger.WithError(err).WithField("service", service).Error("Failed to get service stats")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get service stats: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, stats)
}

// getStorageStats handles storage statistics retrieval
func (h *Handlers) getStorageStats(c *gin.Context) {
	stats, err := h.service.GetStorageStats(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get storage stats")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get storage stats: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, stats)
}

// parseTraceQueryParams parses trace query parameters from the request
func (h *Handlers) parseTraceQueryParams(c *gin.Context) (*TraceQueryParams, error) {
	params := &TraceQueryParams{}
	
	// Bind query parameters
	if err := c.ShouldBindQuery(params); err != nil {
		return nil, err
	}
	
	// Parse duration parameters
	if minDurStr := c.Query("minDuration"); minDurStr != "" {
		minDur, err := h.service.ParseDuration(minDurStr)
		if err != nil {
			return nil, err
		}
		params.MinDuration = minDur
	}
	
	if maxDurStr := c.Query("maxDuration"); maxDurStr != "" {
		maxDur, err := h.service.ParseDuration(maxDurStr)
		if err != nil {
			return nil, err
		}
		params.MaxDuration = maxDur
	}
	
	// Parse time parameters
	if startStr := c.Query("start"); startStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startStr); err == nil {
			params.StartTime = startTime
		} else if timestamp, err := strconv.ParseInt(startStr, 10, 64); err == nil {
			params.StartTime = time.UnixMicro(timestamp)
		}
	}
	
	if endStr := c.Query("end"); endStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endStr); err == nil {
			params.EndTime = endTime
		} else if timestamp, err := strconv.ParseInt(endStr, 10, 64); err == nil {
			params.EndTime = time.UnixMicro(timestamp)
		}
	}
	
	// Parse error filter
	if errorStr := c.Query("hasError"); errorStr != "" {
		if hasError, err := strconv.ParseBool(errorStr); err == nil {
			params.HasError = &hasError
		}
	}
	
	return params, nil
}

// parseTimeRange parses time range parameters from the request
func (h *Handlers) parseTimeRange(c *gin.Context) (*TimeRange, error) {
	var timeRange *TimeRange
	
	startStr := c.Query("start")
	endStr := c.Query("end")
	
	if startStr != "" || endStr != "" {
		timeRange = &TimeRange{}
		
		if startStr != "" {
			if startTime, err := time.Parse(time.RFC3339, startStr); err == nil {
				timeRange.Start = startTime
			} else if timestamp, err := strconv.ParseInt(startStr, 10, 64); err == nil {
				timeRange.Start = time.UnixMicro(timestamp)
			} else {
				return nil, err
			}
		}
		
		if endStr != "" {
			if endTime, err := time.Parse(time.RFC3339, endStr); err == nil {
				timeRange.End = endTime
			} else if timestamp, err := strconv.ParseInt(endStr, 10, 64); err == nil {
				timeRange.End = time.UnixMicro(timestamp)
			} else {
				return nil, err
			}
		}
	}
	
	return timeRange, nil
}
