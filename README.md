# Distributed Tracing Tool

A modern, lightweight distributed tracing tool that provides clear trace visualization, precise problem location, and detailed performance analysis.

## Features

- **Non-intrusive Integration**: Zero code modification required for existing projects
- **Clear Trace Visualization**: Timeline-based trace visualization with service swimlanes
- **Precise Problem Location**: Automatic anomaly detection and root cause analysis
- **Detailed Performance Analysis**: Microsecond-level timing with P95/P99 metrics
- **Multi-language Support**: Java, Node.js, Python, Go, and more
- **Real-time Monitoring**: Live performance monitoring with intelligent alerting
- **Modern Web UI**: React-based interface with 3D service topology visualization
- **Lightweight Deployment**: Single binary deployment with configurable storage backends

## Quick Start

### Prerequisites

- Go 1.21 or later
- Make (optional, for using Makefile)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd distributed-tracing-tool
```

2. Download dependencies:
```bash
make deps
```

3. Build the application:
```bash
make build
```

4. Run the application:
```bash
make run
```

The application will start with:
- HTTP API server on port 8080
- gRPC collector on port 9090
- Web UI available at http://localhost:8080

### Configuration

The application can be configured using:

1. **Configuration file** (config.yaml):
```yaml
server:
  http_port: 8080
  grpc_port: 9090
  host: "0.0.0.0"

storage:
  backend: "sqlite"
  dsn: "./traces.db"

log_level: "info"
```

2. **Command line flags**:
```bash
./bin/distributed-tracing-tool --port 8080 --grpc-port 9090 --storage sqlite --log-level debug
```

3. **Environment variables**:
```bash
export TRACING_SERVER_HTTP_PORT=8080
export TRACING_STORAGE_BACKEND=sqlite
export TRACING_LOG_LEVEL=info
```

## Development

### Development Mode

Run in development mode with hot reload:
```bash
make dev
```

### Testing

Run tests:
```bash
make test
```

Run tests with coverage:
```bash
make test-coverage
```

### Code Quality

Format code:
```bash
make fmt
```

Lint code:
```bash
make lint
```

### Building

Build for current platform:
```bash
make build
```

Build for all platforms:
```bash
make build-all
```

## Architecture

The application follows a modular architecture:

```
├── cmd/                    # Command line tools
├── internal/
│   ├── app/               # Main application
│   ├── config/            # Configuration management
│   ├── collector/         # Data collection services
│   ├── storage/           # Storage adapters
│   ├── query/             # Query services
│   └── models/            # Data models
├── pkg/
│   ├── logger/            # Logging utilities
│   └── utils/             # Common utilities
├── web/                   # Frontend application
├── proto/                 # Protocol buffer definitions
└── docs/                  # Documentation
```

## Storage Backends

The tool supports multiple storage backends:

- **SQLite**: Lightweight, file-based storage (default)
- **ClickHouse**: High-performance columnar database for large-scale deployments
- **PostgreSQL**: Traditional relational database with JSONB support

## API Endpoints

### Health Check
- `GET /health` - Application health status

### Traces API
- `GET /api/v1/traces` - List traces with filtering
- `GET /api/v1/traces/:id` - Get trace by ID

### Services API
- `GET /api/v1/services` - List services
- `GET /api/v1/operations` - List operations

### Collector API
- `POST /api/collector/traces` - Submit trace data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Roadmap

- [x] Basic project structure and configuration
- [ ] Core data models and storage
- [ ] gRPC and HTTP data collectors
- [ ] Query API implementation
- [ ] Web UI with trace visualization
- [ ] Multi-language agents
- [ ] Advanced analytics and alerting
- [ ] Performance optimizations
