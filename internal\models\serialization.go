package models

import (
	"bytes"
	"compress/gzip"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"io"
)

// SerializationFormat represents the format for serialization
type SerializationFormat string

const (
	FormatJSON   SerializationFormat = "json"
	FormatBinary SerializationFormat = "binary"
	FormatGob    SerializationFormat = "gob"
)

// SerializationOptions provides options for serialization
type SerializationOptions struct {
	Format   SerializationFormat `json:"format"`
	Compress bool                `json:"compress"`
	Pretty   bool                `json:"pretty"` // Only for JSON
}

// DefaultSerializationOptions returns default serialization options
func DefaultSerializationOptions() *SerializationOptions {
	return &SerializationOptions{
		Format:   FormatJSON,
		Compress: false,
		Pretty:   false,
	}
}

// Serializer handles serialization and deserialization of trace data
type Serializer struct {
	options *SerializationOptions
}

// NewSerializer creates a new serializer with the given options
func NewSerializer(options *SerializationOptions) *Serializer {
	if options == nil {
		options = DefaultSerializationOptions()
	}
	return &Serializer{options: options}
}

// SerializeTrace serializes a trace to bytes
func (s *Serializer) SerializeTrace(trace *Trace) ([]byte, error) {
	var data []byte
	var err error
	
	switch s.options.Format {
	case FormatJSON:
		if s.options.Pretty {
			data, err = json.MarshalIndent(trace, "", "  ")
		} else {
			data, err = json.Marshal(trace)
		}
	case FormatBinary, FormatGob:
		var buf bytes.Buffer
		encoder := gob.NewEncoder(&buf)
		err = encoder.Encode(trace)
		data = buf.Bytes()
	default:
		return nil, fmt.Errorf("unsupported serialization format: %s", s.options.Format)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to serialize trace: %w", err)
	}
	
	if s.options.Compress {
		return s.compress(data)
	}
	
	return data, nil
}

// DeserializeTrace deserializes bytes to a trace
func (s *Serializer) DeserializeTrace(data []byte) (*Trace, error) {
	if s.options.Compress {
		var err error
		data, err = s.decompress(data)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress data: %w", err)
		}
	}
	
	var trace Trace
	var err error
	
	switch s.options.Format {
	case FormatJSON:
		err = json.Unmarshal(data, &trace)
	case FormatBinary, FormatGob:
		buf := bytes.NewBuffer(data)
		decoder := gob.NewDecoder(buf)
		err = decoder.Decode(&trace)
	default:
		return nil, fmt.Errorf("unsupported serialization format: %s", s.options.Format)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize trace: %w", err)
	}
	
	return &trace, nil
}

// SerializeSpan serializes a span to bytes
func (s *Serializer) SerializeSpan(span *Span) ([]byte, error) {
	var data []byte
	var err error
	
	switch s.options.Format {
	case FormatJSON:
		if s.options.Pretty {
			data, err = json.MarshalIndent(span, "", "  ")
		} else {
			data, err = json.Marshal(span)
		}
	case FormatBinary, FormatGob:
		var buf bytes.Buffer
		encoder := gob.NewEncoder(&buf)
		err = encoder.Encode(span)
		data = buf.Bytes()
	default:
		return nil, fmt.Errorf("unsupported serialization format: %s", s.options.Format)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to serialize span: %w", err)
	}
	
	if s.options.Compress {
		return s.compress(data)
	}
	
	return data, nil
}

// DeserializeSpan deserializes bytes to a span
func (s *Serializer) DeserializeSpan(data []byte) (*Span, error) {
	if s.options.Compress {
		var err error
		data, err = s.decompress(data)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress data: %w", err)
		}
	}
	
	var span Span
	var err error
	
	switch s.options.Format {
	case FormatJSON:
		err = json.Unmarshal(data, &span)
	case FormatBinary, FormatGob:
		buf := bytes.NewBuffer(data)
		decoder := gob.NewDecoder(buf)
		err = decoder.Decode(&span)
	default:
		return nil, fmt.Errorf("unsupported serialization format: %s", s.options.Format)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize span: %w", err)
	}
	
	return &span, nil
}

// SerializeSpanBatch serializes a batch of spans to bytes
func (s *Serializer) SerializeSpanBatch(spans []*Span) ([]byte, error) {
	var data []byte
	var err error
	
	switch s.options.Format {
	case FormatJSON:
		if s.options.Pretty {
			data, err = json.MarshalIndent(spans, "", "  ")
		} else {
			data, err = json.Marshal(spans)
		}
	case FormatBinary, FormatGob:
		var buf bytes.Buffer
		encoder := gob.NewEncoder(&buf)
		err = encoder.Encode(spans)
		data = buf.Bytes()
	default:
		return nil, fmt.Errorf("unsupported serialization format: %s", s.options.Format)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to serialize span batch: %w", err)
	}
	
	if s.options.Compress {
		return s.compress(data)
	}
	
	return data, nil
}

// DeserializeSpanBatch deserializes bytes to a batch of spans
func (s *Serializer) DeserializeSpanBatch(data []byte) ([]*Span, error) {
	if s.options.Compress {
		var err error
		data, err = s.decompress(data)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress data: %w", err)
		}
	}
	
	var spans []*Span
	var err error
	
	switch s.options.Format {
	case FormatJSON:
		err = json.Unmarshal(data, &spans)
	case FormatBinary, FormatGob:
		buf := bytes.NewBuffer(data)
		decoder := gob.NewDecoder(buf)
		err = decoder.Decode(&spans)
	default:
		return nil, fmt.Errorf("unsupported serialization format: %s", s.options.Format)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize span batch: %w", err)
	}
	
	return spans, nil
}

// compress compresses data using gzip
func (s *Serializer) compress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	
	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, err
	}
	
	if err := writer.Close(); err != nil {
		return nil, err
	}
	
	return buf.Bytes(), nil
}

// decompress decompresses gzip data
func (s *Serializer) decompress(data []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()
	
	return io.ReadAll(reader)
}

// EstimateSize estimates the serialized size of a trace
func (s *Serializer) EstimateSize(trace *Trace) (int, error) {
	data, err := s.SerializeTrace(trace)
	if err != nil {
		return 0, err
	}
	return len(data), nil
}

// EstimateSpanSize estimates the serialized size of a span
func (s *Serializer) EstimateSpanSize(span *Span) (int, error) {
	data, err := s.SerializeSpan(span)
	if err != nil {
		return 0, err
	}
	return len(data), nil
}

// ToJSON converts a trace to JSON string
func ToJSON(trace *Trace, pretty bool) (string, error) {
	serializer := NewSerializer(&SerializationOptions{
		Format: FormatJSON,
		Pretty: pretty,
	})
	
	data, err := serializer.SerializeTrace(trace)
	if err != nil {
		return "", err
	}
	
	return string(data), nil
}

// FromJSON converts JSON string to a trace
func FromJSON(jsonStr string) (*Trace, error) {
	serializer := NewSerializer(&SerializationOptions{
		Format: FormatJSON,
	})
	
	return serializer.DeserializeTrace([]byte(jsonStr))
}

// SpanToJSON converts a span to JSON string
func SpanToJSON(span *Span, pretty bool) (string, error) {
	serializer := NewSerializer(&SerializationOptions{
		Format: FormatJSON,
		Pretty: pretty,
	})
	
	data, err := serializer.SerializeSpan(span)
	if err != nil {
		return "", err
	}
	
	return string(data), nil
}

// SpanFromJSON converts JSON string to a span
func SpanFromJSON(jsonStr string) (*Span, error) {
	serializer := NewSerializer(&SerializationOptions{
		Format: FormatJSON,
	})
	
	return serializer.DeserializeSpan([]byte(jsonStr))
}
