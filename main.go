package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"distributed-tracing-tool/internal/app"
	"distributed-tracing-tool/internal/config"
	"distributed-tracing-tool/pkg/logger"

	"github.com/spf13/cobra"
)

var (
	version = "dev"
	commit  = "unknown"
	date    = "unknown"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "distributed-tracing-tool",
		Short: "A modern distributed tracing tool",
		Long: `A lightweight, high-performance distributed tracing tool 
that provides clear trace visualization, precise problem location, 
and detailed performance analysis.`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE:    run,
	}

	// Add command line flags
	rootCmd.PersistentFlags().StringP("config", "c", "", "config file path")
	rootCmd.PersistentFlags().StringP("log-level", "l", "info", "log level (debug, info, warn, error)")
	rootCmd.PersistentFlags().IntP("port", "p", 8080, "HTTP server port")
	rootCmd.PersistentFlags().Int("grpc-port", 9090, "gRPC server port")
	rootCmd.PersistentFlags().String("storage", "sqlite", "storage backend (sqlite, clickhouse, postgresql)")
	rootCmd.PersistentFlags().String("storage-dsn", "./traces.db", "storage data source name")

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func run(cmd *cobra.Command, args []string) error {
	// Load configuration
	cfg, err := config.Load(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Initialize logger
	log := logger.New(cfg.LogLevel)
	log.WithField("version", version).Info("Starting distributed tracing tool")

	// Create application
	application, err := app.New(cfg, log)
	if err != nil {
		return fmt.Errorf("failed to create application: %w", err)
	}

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Info("Received shutdown signal")
		cancel()
	}()

	// Start application
	if err := application.Run(ctx); err != nil {
		return fmt.Errorf("application error: %w", err)
	}

	log.Info("Application stopped gracefully")
	return nil
}
