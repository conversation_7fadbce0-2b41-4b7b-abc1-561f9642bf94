package sqlite

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"distributed-tracing-tool/internal/models"
	"distributed-tracing-tool/internal/storage"
	"distributed-tracing-tool/pkg/logger"

	_ "github.com/mattn/go-sqlite3"
)

// SQLiteStorage implements the Storage interface using SQLite
type SQLiteStorage struct {
	db     *sql.DB
	logger *logger.Logger
	config *Config
}

// Config holds SQLite-specific configuration
type Config struct {
	Path           string `json:"path"`
	MaxConnections int    `json:"maxConnections"`
	BusyTimeout    int    `json:"busyTimeout"` // milliseconds
	WALMode        bool   `json:"walMode"`
	SyncMode       string `json:"syncMode"` // OFF, NORMAL, FULL
}

// DefaultConfig returns default SQLite configuration
func DefaultConfig() *Config {
	return &Config{
		Path:           "./traces.db",
		MaxConnections: 10,
		BusyTimeout:    30000, // 30 seconds
		WALMode:        true,
		SyncMode:       "NORMAL",
	}
}

// New creates a new SQLite storage instance
func New(logger *logger.Logger, config *Config) (*SQLiteStorage, error) {
	if config == nil {
		config = DefaultConfig()
	}
	
	storage := &SQLiteStorage{
		logger: logger,
		config: config,
	}
	
	return storage, nil
}

// Initialize initializes the SQLite database
func (s *SQLiteStorage) Initialize(ctx context.Context) error {
	s.logger.WithField("path", s.config.Path).Info("Initializing SQLite storage")
	
	// Open database connection
	db, err := sql.Open("sqlite3", s.buildConnectionString())
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}
	
	s.db = db
	
	// Configure connection pool
	s.db.SetMaxOpenConns(s.config.MaxConnections)
	s.db.SetMaxIdleConns(s.config.MaxConnections / 2)
	s.db.SetConnMaxLifetime(time.Hour)
	
	// Test connection
	if err := s.db.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}
	
	// Create tables
	if err := s.createTables(ctx); err != nil {
		return fmt.Errorf("failed to create tables: %w", err)
	}
	
	// Create indexes
	if err := s.createIndexes(ctx); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}
	
	s.logger.Info("SQLite storage initialized successfully")
	return nil
}

// Close closes the database connection
func (s *SQLiteStorage) Close() error {
	if s.db != nil {
		s.logger.Info("Closing SQLite storage")
		return s.db.Close()
	}
	return nil
}

// HealthCheck checks the health of the storage
func (s *SQLiteStorage) HealthCheck(ctx context.Context) error {
	if s.db == nil {
		return fmt.Errorf("database not initialized")
	}
	
	return s.db.PingContext(ctx)
}

// WriteTrace writes a trace to storage
func (s *SQLiteStorage) WriteTrace(ctx context.Context, trace *models.Trace) error {
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	
	// Insert trace
	if err := s.insertTrace(ctx, tx, trace); err != nil {
		return fmt.Errorf("failed to insert trace: %w", err)
	}
	
	// Insert processes
	for processID, process := range trace.Processes {
		if err := s.insertProcess(ctx, tx, trace.TraceID, processID, &process); err != nil {
			return fmt.Errorf("failed to insert process: %w", err)
		}
	}
	
	// Insert spans
	for _, span := range trace.Spans {
		if err := s.insertSpan(ctx, tx, span); err != nil {
			return fmt.Errorf("failed to insert span: %w", err)
		}
	}
	
	return tx.Commit()
}

// WriteBatch writes a batch of traces to storage
func (s *SQLiteStorage) WriteBatch(ctx context.Context, traces []*models.Trace) error {
	if len(traces) == 0 {
		return nil
	}
	
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	
	for _, trace := range traces {
		// Insert trace
		if err := s.insertTrace(ctx, tx, trace); err != nil {
			s.logger.WithError(err).WithField("trace_id", trace.TraceID).Error("Failed to insert trace in batch")
			continue
		}
		
		// Insert processes
		for processID, process := range trace.Processes {
			if err := s.insertProcess(ctx, tx, trace.TraceID, processID, &process); err != nil {
				s.logger.WithError(err).WithField("trace_id", trace.TraceID).Error("Failed to insert process in batch")
			}
		}
		
		// Insert spans
		for _, span := range trace.Spans {
			if err := s.insertSpan(ctx, tx, span); err != nil {
				s.logger.WithError(err).WithField("span_id", span.SpanID).Error("Failed to insert span in batch")
			}
		}
	}
	
	return tx.Commit()
}

// GetTrace retrieves a trace by ID
func (s *SQLiteStorage) GetTrace(ctx context.Context, traceID models.TraceID) (*models.Trace, error) {
	// Get trace metadata
	trace, err := s.getTraceMetadata(ctx, traceID)
	if err != nil {
		return nil, err
	}
	
	// Get processes
	processes, err := s.getTraceProcesses(ctx, traceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get processes: %w", err)
	}
	trace.Processes = processes
	
	// Get spans
	spans, err := s.GetTraceSpans(ctx, traceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get spans: %w", err)
	}
	trace.Spans = spans
	
	return trace, nil
}

// WriteSpan writes a single span to storage
func (s *SQLiteStorage) WriteSpan(ctx context.Context, span *models.Span) error {
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	
	if err := s.insertSpan(ctx, tx, span); err != nil {
		return fmt.Errorf("failed to insert span: %w", err)
	}
	
	return tx.Commit()
}

// WriteSpanBatch writes a batch of spans to storage
func (s *SQLiteStorage) WriteSpanBatch(ctx context.Context, spans []*models.Span) error {
	if len(spans) == 0 {
		return nil
	}
	
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	
	for _, span := range spans {
		if err := s.insertSpan(ctx, tx, span); err != nil {
			s.logger.WithError(err).WithField("span_id", span.SpanID).Error("Failed to insert span in batch")
		}
	}
	
	return tx.Commit()
}

// GetSpan retrieves a span by trace ID and span ID
func (s *SQLiteStorage) GetSpan(ctx context.Context, traceID models.TraceID, spanID models.SpanID) (*models.Span, error) {
	query := `
		SELECT trace_id, span_id, parent_span_id, operation_name, start_time, duration,
		       process_id, tags, logs, references, warnings, kind, status_code, status_message
		FROM spans 
		WHERE trace_id = ? AND span_id = ?
	`
	
	row := s.db.QueryRowContext(ctx, query, traceID, spanID)
	
	span, err := s.scanSpan(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("span not found: trace_id=%s, span_id=%s", traceID, spanID)
		}
		return nil, fmt.Errorf("failed to scan span: %w", err)
	}
	
	return span, nil
}

// GetTraceSpans retrieves all spans for a trace
func (s *SQLiteStorage) GetTraceSpans(ctx context.Context, traceID models.TraceID) ([]*models.Span, error) {
	query := `
		SELECT trace_id, span_id, parent_span_id, operation_name, start_time, duration,
		       process_id, tags, logs, references, warnings, kind, status_code, status_message
		FROM spans 
		WHERE trace_id = ?
		ORDER BY start_time ASC
	`
	
	rows, err := s.db.QueryContext(ctx, query, traceID)
	if err != nil {
		return nil, fmt.Errorf("failed to query spans: %w", err)
	}
	defer rows.Close()
	
	var spans []*models.Span
	for rows.Next() {
		span, err := s.scanSpan(rows)
		if err != nil {
			s.logger.WithError(err).Error("Failed to scan span")
			continue
		}
		spans = append(spans, span)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating spans: %w", err)
	}
	
	return spans, nil
}

// buildConnectionString builds the SQLite connection string
func (s *SQLiteStorage) buildConnectionString() string {
	params := fmt.Sprintf("?_busy_timeout=%d", s.config.BusyTimeout)

	if s.config.WALMode {
		params += "&_journal_mode=WAL"
	}

	params += fmt.Sprintf("&_synchronous=%s", s.config.SyncMode)
	params += "&_foreign_keys=ON"

	return s.config.Path + params
}

// createTables creates the database tables
func (s *SQLiteStorage) createTables(ctx context.Context) error {
	tables := []string{
		`CREATE TABLE IF NOT EXISTS traces (
			trace_id TEXT PRIMARY KEY,
			start_time INTEGER NOT NULL,
			end_time INTEGER NOT NULL,
			duration INTEGER NOT NULL,
			service_count INTEGER NOT NULL DEFAULT 0,
			span_count INTEGER NOT NULL DEFAULT 0,
			error_count INTEGER NOT NULL DEFAULT 0,
			tags TEXT,
			warnings TEXT,
			created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
		)`,

		`CREATE TABLE IF NOT EXISTS processes (
			trace_id TEXT NOT NULL,
			process_id TEXT NOT NULL,
			service_name TEXT NOT NULL,
			tags TEXT,
			PRIMARY KEY (trace_id, process_id),
			FOREIGN KEY (trace_id) REFERENCES traces(trace_id) ON DELETE CASCADE
		)`,

		`CREATE TABLE IF NOT EXISTS spans (
			trace_id TEXT NOT NULL,
			span_id TEXT NOT NULL,
			parent_span_id TEXT,
			operation_name TEXT NOT NULL,
			start_time INTEGER NOT NULL,
			duration INTEGER NOT NULL,
			process_id TEXT NOT NULL,
			tags TEXT,
			logs TEXT,
			references TEXT,
			warnings TEXT,
			kind TEXT,
			status_code INTEGER,
			status_message TEXT,
			PRIMARY KEY (trace_id, span_id),
			FOREIGN KEY (trace_id) REFERENCES traces(trace_id) ON DELETE CASCADE
		)`,
	}

	for _, table := range tables {
		if _, err := s.db.ExecContext(ctx, table); err != nil {
			return fmt.Errorf("failed to create table: %w", err)
		}
	}

	return nil
}

// createIndexes creates database indexes for performance
func (s *SQLiteStorage) createIndexes(ctx context.Context) error {
	indexes := []string{
		`CREATE INDEX IF NOT EXISTS idx_traces_start_time ON traces(start_time)`,
		`CREATE INDEX IF NOT EXISTS idx_traces_duration ON traces(duration)`,
		`CREATE INDEX IF NOT EXISTS idx_traces_service_count ON traces(service_count)`,
		`CREATE INDEX IF NOT EXISTS idx_traces_error_count ON traces(error_count)`,

		`CREATE INDEX IF NOT EXISTS idx_processes_service_name ON processes(service_name)`,

		`CREATE INDEX IF NOT EXISTS idx_spans_trace_id ON spans(trace_id)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_parent_span_id ON spans(parent_span_id)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_operation_name ON spans(operation_name)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_start_time ON spans(start_time)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_duration ON spans(duration)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_process_id ON spans(process_id)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_kind ON spans(kind)`,
		`CREATE INDEX IF NOT EXISTS idx_spans_status_code ON spans(status_code)`,
	}

	for _, index := range indexes {
		if _, err := s.db.ExecContext(ctx, index); err != nil {
			return fmt.Errorf("failed to create index: %w", err)
		}
	}

	return nil
}

// insertTrace inserts a trace into the database
func (s *SQLiteStorage) insertTrace(ctx context.Context, tx *sql.Tx, trace *models.Trace) error {
	tagsJSON, _ := json.Marshal(trace.Tags)
	warningsJSON, _ := json.Marshal(trace.Warnings)

	query := `
		INSERT OR REPLACE INTO traces
		(trace_id, start_time, end_time, duration, service_count, span_count, error_count, tags, warnings)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := tx.ExecContext(ctx, query,
		trace.TraceID,
		trace.StartTime.UnixMicro(),
		trace.EndTime.UnixMicro(),
		trace.Duration,
		trace.ServiceCount,
		trace.SpanCount,
		trace.ErrorCount,
		string(tagsJSON),
		string(warningsJSON),
	)

	return err
}

// insertProcess inserts a process into the database
func (s *SQLiteStorage) insertProcess(ctx context.Context, tx *sql.Tx, traceID models.TraceID, processID string, process *models.Process) error {
	tagsJSON, _ := json.Marshal(process.Tags)

	query := `
		INSERT OR REPLACE INTO processes
		(trace_id, process_id, service_name, tags)
		VALUES (?, ?, ?, ?)
	`

	_, err := tx.ExecContext(ctx, query,
		traceID,
		processID,
		process.ServiceName,
		string(tagsJSON),
	)

	return err
}

// insertSpan inserts a span into the database
func (s *SQLiteStorage) insertSpan(ctx context.Context, tx *sql.Tx, span *models.Span) error {
	tagsJSON, _ := json.Marshal(span.Tags)
	logsJSON, _ := json.Marshal(span.Logs)
	referencesJSON, _ := json.Marshal(span.References)
	warningsJSON, _ := json.Marshal(span.Warnings)

	query := `
		INSERT OR REPLACE INTO spans
		(trace_id, span_id, parent_span_id, operation_name, start_time, duration,
		 process_id, tags, logs, references, warnings, kind, status_code, status_message)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	var parentSpanID interface{}
	if span.ParentSpanID != "" {
		parentSpanID = span.ParentSpanID
	}

	_, err := tx.ExecContext(ctx, query,
		span.TraceID,
		span.SpanID,
		parentSpanID,
		span.OperationName,
		span.StartTime,
		span.Duration,
		span.ProcessID,
		string(tagsJSON),
		string(logsJSON),
		string(referencesJSON),
		string(warningsJSON),
		span.Kind,
		span.StatusCode,
		span.StatusMessage,
	)

	return err
}

// getTraceMetadata retrieves trace metadata
func (s *SQLiteStorage) getTraceMetadata(ctx context.Context, traceID models.TraceID) (*models.Trace, error) {
	query := `
		SELECT trace_id, start_time, end_time, duration, service_count, span_count, error_count, tags, warnings
		FROM traces
		WHERE trace_id = ?
	`

	row := s.db.QueryRowContext(ctx, query, traceID)

	var trace models.Trace
	var startTime, endTime int64
	var tagsJSON, warningsJSON string

	err := row.Scan(
		&trace.TraceID,
		&startTime,
		&endTime,
		&trace.Duration,
		&trace.ServiceCount,
		&trace.SpanCount,
		&trace.ErrorCount,
		&tagsJSON,
		&warningsJSON,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trace not found: %s", traceID)
		}
		return nil, fmt.Errorf("failed to scan trace: %w", err)
	}

	// Convert timestamps
	trace.StartTime = time.UnixMicro(startTime)
	trace.EndTime = time.UnixMicro(endTime)

	// Parse JSON fields
	if tagsJSON != "" {
		json.Unmarshal([]byte(tagsJSON), &trace.Tags)
	}
	if warningsJSON != "" {
		json.Unmarshal([]byte(warningsJSON), &trace.Warnings)
	}

	return &trace, nil
}

// getTraceProcesses retrieves all processes for a trace
func (s *SQLiteStorage) getTraceProcesses(ctx context.Context, traceID models.TraceID) (map[string]models.Process, error) {
	query := `
		SELECT process_id, service_name, tags
		FROM processes
		WHERE trace_id = ?
	`

	rows, err := s.db.QueryContext(ctx, query, traceID)
	if err != nil {
		return nil, fmt.Errorf("failed to query processes: %w", err)
	}
	defer rows.Close()

	processes := make(map[string]models.Process)
	for rows.Next() {
		var processID, serviceName, tagsJSON string

		if err := rows.Scan(&processID, &serviceName, &tagsJSON); err != nil {
			s.logger.WithError(err).Error("Failed to scan process")
			continue
		}

		process := models.Process{
			ServiceName: serviceName,
		}

		if tagsJSON != "" {
			json.Unmarshal([]byte(tagsJSON), &process.Tags)
		}

		processes[processID] = process
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating processes: %w", err)
	}

	return processes, nil
}

// scanSpan scans a span from a database row
func (s *SQLiteStorage) scanSpan(scanner interface {
	Scan(dest ...interface{}) error
}) (*models.Span, error) {
	var span models.Span
	var parentSpanID sql.NullString
	var tagsJSON, logsJSON, referencesJSON, warningsJSON string
	var statusCode sql.NullInt32
	var statusMessage sql.NullString

	err := scanner.Scan(
		&span.TraceID,
		&span.SpanID,
		&parentSpanID,
		&span.OperationName,
		&span.StartTime,
		&span.Duration,
		&span.ProcessID,
		&tagsJSON,
		&logsJSON,
		&referencesJSON,
		&warningsJSON,
		&span.Kind,
		&statusCode,
		&statusMessage,
	)

	if err != nil {
		return nil, err
	}

	// Handle nullable fields
	if parentSpanID.Valid {
		span.ParentSpanID = models.SpanID(parentSpanID.String)
	}
	if statusCode.Valid {
		span.StatusCode = statusCode.Int32
	}
	if statusMessage.Valid {
		span.StatusMessage = statusMessage.String
	}

	// Parse JSON fields
	if tagsJSON != "" {
		json.Unmarshal([]byte(tagsJSON), &span.Tags)
	}
	if logsJSON != "" {
		json.Unmarshal([]byte(logsJSON), &span.Logs)
	}
	if referencesJSON != "" {
		json.Unmarshal([]byte(referencesJSON), &span.References)
	}
	if warningsJSON != "" {
		json.Unmarshal([]byte(warningsJSON), &span.Warnings)
	}

	return &span, nil
}
