package storage

import (
	"context"
	"fmt"

	"distributed-tracing-tool/internal/config"
	"distributed-tracing-tool/internal/storage/sqlite"
	"distributed-tracing-tool/pkg/logger"
)

// Factory creates storage instances based on configuration
type Factory struct {
	logger *logger.Logger
}

// NewFactory creates a new storage factory
func NewFactory(logger *logger.Logger) *Factory {
	return &Factory{
		logger: logger,
	}
}

// CreateStorage creates a storage instance based on the configuration
func (f *Factory) CreateStorage(cfg *config.Config) (Storage, error) {
	switch cfg.Storage.Backend {
	case "sqlite":
		return f.createSQLiteStorage(cfg)
	case "clickhouse":
		return nil, fmt.Errorf("ClickHouse storage not yet implemented")
	case "postgresql":
		return nil, fmt.Errorf("PostgreSQL storage not yet implemented")
	default:
		return nil, fmt.Errorf("unsupported storage backend: %s", cfg.Storage.Backend)
	}
}

// createSQLiteStorage creates a SQLite storage instance
func (f *Factory) createSQLiteStorage(cfg *config.Config) (Storage, error) {
	sqliteConfig := &sqlite.Config{
		Path:           cfg.Storage.DSN,
		MaxConnections: cfg.Storage.SQLite.MaxConnections,
		BusyTimeout:    30000, // 30 seconds
		WALMode:        true,
		SyncMode:       "NORMAL",
	}
	
	// Override with specific SQLite config if provided
	if cfg.Storage.SQLite.Path != "" {
		sqliteConfig.Path = cfg.Storage.SQLite.Path
	}
	if cfg.Storage.SQLite.MaxConnections > 0 {
		sqliteConfig.MaxConnections = cfg.Storage.SQLite.MaxConnections
	}
	
	storage, err := sqlite.New(f.logger.WithComponent("sqlite-storage"), sqliteConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create SQLite storage: %w", err)
	}
	
	return storage, nil
}

// InitializeStorage creates and initializes a storage instance
func (f *Factory) InitializeStorage(ctx context.Context, cfg *config.Config) (Storage, error) {
	f.logger.WithField("backend", cfg.Storage.Backend).Info("Initializing storage")
	
	storage, err := f.CreateStorage(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create storage: %w", err)
	}
	
	if err := storage.Initialize(ctx); err != nil {
		return nil, fmt.Errorf("failed to initialize storage: %w", err)
	}
	
	f.logger.Info("Storage initialized successfully")
	return storage, nil
}

// ValidateConfig validates storage configuration
func (f *Factory) ValidateConfig(cfg *config.Config) error {
	if cfg.Storage.Backend == "" {
		return fmt.Errorf("storage backend not specified")
	}
	
	switch cfg.Storage.Backend {
	case "sqlite":
		if cfg.Storage.DSN == "" && cfg.Storage.SQLite.Path == "" {
			return fmt.Errorf("SQLite path not specified")
		}
	case "clickhouse":
		if len(cfg.Storage.ClickHouse.Addresses) == 0 {
			return fmt.Errorf("ClickHouse addresses not specified")
		}
		if cfg.Storage.ClickHouse.Database == "" {
			return fmt.Errorf("ClickHouse database not specified")
		}
	case "postgresql":
		if cfg.Storage.PostgreSQL.Host == "" {
			return fmt.Errorf("PostgreSQL host not specified")
		}
		if cfg.Storage.PostgreSQL.Database == "" {
			return fmt.Errorf("PostgreSQL database not specified")
		}
	default:
		return fmt.Errorf("unsupported storage backend: %s", cfg.Storage.Backend)
	}
	
	return nil
}

// GetStorageInfo returns information about the storage backend
func (f *Factory) GetStorageInfo(cfg *config.Config) map[string]interface{} {
	info := map[string]interface{}{
		"backend": cfg.Storage.Backend,
		"dsn":     cfg.Storage.DSN,
	}
	
	switch cfg.Storage.Backend {
	case "sqlite":
		info["path"] = cfg.Storage.SQLite.Path
		info["max_connections"] = cfg.Storage.SQLite.MaxConnections
	case "clickhouse":
		info["addresses"] = cfg.Storage.ClickHouse.Addresses
		info["database"] = cfg.Storage.ClickHouse.Database
		info["username"] = cfg.Storage.ClickHouse.Username
	case "postgresql":
		info["host"] = cfg.Storage.PostgreSQL.Host
		info["port"] = cfg.Storage.PostgreSQL.Port
		info["database"] = cfg.Storage.PostgreSQL.Database
		info["username"] = cfg.Storage.PostgreSQL.Username
		info["ssl_mode"] = cfg.Storage.PostgreSQL.SSLMode
	}
	
	return info
}
