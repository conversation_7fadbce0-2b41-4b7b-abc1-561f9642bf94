package collector

import (
	"context"
	"fmt"
	"sync"

	"distributed-tracing-tool/internal/config"
	"distributed-tracing-tool/internal/storage"
	"distributed-tracing-tool/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Manager manages all data collectors
type Manager struct {
	logger *logger.Logger
	config *config.Config
	
	// Storage backend
	storage storage.Storage
	
	// Processor
	processor *Processor
	
	// Collectors
	httpCollector *HTTPCollector
	grpcCollector *GRPCCollector
	
	// State
	running bool
	mutex   sync.RWMutex
}

// NewManager creates a new collector manager
func NewManager(logger *logger.Logger, cfg *config.Config, storage storage.Storage) (*Manager, error) {
	// Create processor
	processorConfig := &ProcessorConfig{
		BatchSize:     cfg.Collector.BatchSize,
		BatchTimeout:  cfg.Collector.BatchTimeout * 1000000, // convert seconds to microseconds
		QueueSize:     cfg.Collector.QueueSize,
		WorkerCount:   4, // TODO: make configurable
		ValidateData:  true,
		SanitizeData:  true,
		EnableMetrics: true,
	}
	
	processor := NewProcessor(logger.WithComponent("processor"), storage, processorConfig)
	
	// Create HTTP collector
	httpConfig := &HTTPCollectorConfig{
		MaxRequestSize: 10 * 1024 * 1024, // 10MB
		Timeout:        30000000,          // 30 seconds in microseconds
		RateLimit:      1000,              // 1000 requests per second
	}
	httpCollector := NewHTTPCollector(logger.WithComponent("http-collector"), processor, httpConfig)
	
	// Create gRPC collector
	grpcConfig := &GRPCCollectorConfig{
		Port:           cfg.Server.GRPCPort,
		MaxMessageSize: 10 * 1024 * 1024, // 10MB
		Timeout:        30000000,          // 30 seconds in microseconds
		TLSEnabled:     false,
	}
	grpcCollector := NewGRPCCollector(logger.WithComponent("grpc-collector"), processor, grpcConfig)
	
	return &Manager{
		logger:        logger,
		config:        cfg,
		storage:       storage,
		processor:     processor,
		httpCollector: httpCollector,
		grpcCollector: grpcCollector,
	}, nil
}

// Start starts all collectors
func (m *Manager) Start(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if m.running {
		return fmt.Errorf("collector manager is already running")
	}
	
	m.logger.Info("Starting collector manager")
	
	// Start gRPC collector in background
	go func() {
		if err := m.grpcCollector.Start(ctx); err != nil {
			m.logger.WithError(err).Error("gRPC collector error")
		}
	}()
	
	m.running = true
	m.logger.Info("Collector manager started")
	
	return nil
}

// Stop stops all collectors
func (m *Manager) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if !m.running {
		return nil
	}
	
	m.logger.Info("Stopping collector manager")
	
	// Stop gRPC collector
	m.grpcCollector.Stop()
	
	// Stop processor
	if err := m.processor.Stop(); err != nil {
		m.logger.WithError(err).Error("Failed to stop processor")
	}
	
	m.running = false
	m.logger.Info("Collector manager stopped")
	
	return nil
}

// RegisterHTTPRoutes registers HTTP collector routes
func (m *Manager) RegisterHTTPRoutes(router *gin.Engine) {
	m.httpCollector.RegisterRoutes(router)
}

// GetMetrics returns collector metrics
func (m *Manager) GetMetrics() *CollectorMetrics {
	processorMetrics := m.processor.GetMetrics()
	
	return &CollectorMetrics{
		Processor: processorMetrics,
		HTTP: &HTTPCollectorMetrics{
			RequestsReceived: 0, // TODO: implement HTTP metrics
			RequestsDropped:  0,
			AverageLatency:   0,
		},
		GRPC: &GRPCCollectorMetrics{
			RequestsReceived: 0, // TODO: implement gRPC metrics
			RequestsDropped:  0,
			AverageLatency:   0,
		},
	}
}

// GetStatus returns collector status
func (m *Manager) GetStatus() *CollectorStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	return &CollectorStatus{
		Running:   m.running,
		HTTPPort:  m.config.Server.HTTPPort,
		GRPCPort:  m.config.Server.GRPCPort,
		Processor: m.processor.GetMetrics(),
	}
}

// IsHealthy checks if all collectors are healthy
func (m *Manager) IsHealthy() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	if !m.running {
		return false
	}
	
	// Check processor health
	metrics := m.processor.GetMetrics()
	if metrics.QueueDepth > m.processor.config.QueueSize*0.9 { // 90% full
		return false
	}
	
	return true
}

// CollectorMetrics holds metrics for all collectors
type CollectorMetrics struct {
	Processor *ProcessorMetrics      `json:"processor"`
	HTTP      *HTTPCollectorMetrics  `json:"http"`
	GRPC      *GRPCCollectorMetrics  `json:"grpc"`
}

// HTTPCollectorMetrics holds HTTP collector metrics
type HTTPCollectorMetrics struct {
	RequestsReceived int64 `json:"requestsReceived"`
	RequestsDropped  int64 `json:"requestsDropped"`
	AverageLatency   int64 `json:"averageLatency"` // microseconds
}

// GRPCCollectorMetrics holds gRPC collector metrics
type GRPCCollectorMetrics struct {
	RequestsReceived int64 `json:"requestsReceived"`
	RequestsDropped  int64 `json:"requestsDropped"`
	AverageLatency   int64 `json:"averageLatency"` // microseconds
}

// CollectorStatus holds collector status information
type CollectorStatus struct {
	Running   bool              `json:"running"`
	HTTPPort  int               `json:"httpPort"`
	GRPCPort  int               `json:"grpcPort"`
	Processor *ProcessorMetrics `json:"processor"`
}

// ProcessTrace processes a trace through the manager
func (m *Manager) ProcessTrace(ctx context.Context, trace *models.Trace) error {
	return m.processor.ProcessTrace(ctx, trace)
}

// ProcessSpan processes a span through the manager
func (m *Manager) ProcessSpan(ctx context.Context, span *models.Span) error {
	return m.processor.ProcessSpan(ctx, span)
}

// ProcessBatch processes a batch of traces and spans through the manager
func (m *Manager) ProcessBatch(ctx context.Context, traces []*models.Trace, spans []*models.Span) error {
	return m.processor.ProcessBatch(ctx, traces, spans)
}
