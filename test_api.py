#!/usr/bin/env python3
"""
Simple test script to verify the distributed tracing tool API
"""

import json
import time
import requests
import random
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8080"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def create_sample_trace():
    """Create a sample trace for testing"""
    trace_id = f"trace-{random.randint(1000, 9999)}"
    start_time = int((datetime.now() - timedelta(minutes=random.randint(1, 60))).timestamp() * 1000000)
    
    # Create spans
    spans = []
    processes = {}
    
    # Root span
    root_span_id = f"span-{random.randint(1000, 9999)}"
    root_duration = random.randint(100000, 5000000)  # 100ms to 5s in microseconds
    
    spans.append({
        "traceID": trace_id,
        "spanID": root_span_id,
        "operationName": "http_request",
        "startTime": start_time,
        "duration": root_duration,
        "processID": "p1",
        "tags": [
            {"key": "http.method", "value": "GET", "type": "string"},
            {"key": "http.url", "value": "/api/users", "type": "string"},
            {"key": "http.status_code", "value": 200, "type": "int64"}
        ],
        "kind": "server"
    })
    
    # Child span
    child_span_id = f"span-{random.randint(1000, 9999)}"
    child_start = start_time + 10000  # 10ms after root
    child_duration = random.randint(50000, 1000000)  # 50ms to 1s
    
    spans.append({
        "traceID": trace_id,
        "spanID": child_span_id,
        "parentSpanID": root_span_id,
        "operationName": "db_query",
        "startTime": child_start,
        "duration": child_duration,
        "processID": "p2",
        "tags": [
            {"key": "db.statement", "value": "SELECT * FROM users", "type": "string"},
            {"key": "db.type", "value": "postgresql", "type": "string"}
        ],
        "kind": "client"
    })
    
    # Processes
    processes["p1"] = {
        "serviceName": "user-service",
        "tags": [
            {"key": "hostname", "value": "web-01", "type": "string"},
            {"key": "version", "value": "1.0.0", "type": "string"}
        ]
    }
    
    processes["p2"] = {
        "serviceName": "database",
        "tags": [
            {"key": "hostname", "value": "db-01", "type": "string"},
            {"key": "version", "value": "13.4", "type": "string"}
        ]
    }
    
    # Create trace
    trace = {
        "traceID": trace_id,
        "spans": spans,
        "processes": processes,
        "startTime": datetime.fromtimestamp(start_time / 1000000).isoformat() + "Z",
        "endTime": datetime.fromtimestamp((start_time + root_duration) / 1000000).isoformat() + "Z",
        "duration": root_duration,
        "serviceCount": 2,
        "spanCount": 2,
        "errorCount": 0
    }
    
    return trace

def test_trace_submission():
    """Test trace submission via collector API"""
    print("\nTesting trace submission...")
    
    # Create sample traces
    traces = [create_sample_trace() for _ in range(3)]
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/collector/traces",
            json=traces,
            headers={"Content-Type": "application/json"}
        )
        print(f"Trace submission: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Processed: {result.get('processed', 0)}, Failed: {result.get('failed', 0)}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Trace submission failed: {e}")
        return False

def test_query_api():
    """Test query API endpoints"""
    print("\nTesting query API...")
    
    # Wait a bit for traces to be processed
    time.sleep(2)
    
    # Test services endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/v1/services")
        print(f"Services query: {response.status_code}")
        if response.status_code == 200:
            services = response.json().get('services', [])
            print(f"Found services: {services}")
        
        # Test traces search
        response = requests.get(f"{BASE_URL}/api/v1/traces?limit=10")
        print(f"Traces search: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            traces = result.get('traces', [])
            print(f"Found {len(traces)} traces")
            
            # Test individual trace retrieval
            if traces:
                trace_id = traces[0]['traceID']
                response = requests.get(f"{BASE_URL}/api/v1/traces/{trace_id}")
                print(f"Individual trace query: {response.status_code}")
                if response.status_code == 200:
                    trace = response.json()
                    print(f"Trace {trace_id} has {len(trace.get('spans', []))} spans")
        
        # Test storage stats
        response = requests.get(f"{BASE_URL}/api/v1/stats")
        print(f"Storage stats: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"Storage stats: {stats}")
        
        return True
    except Exception as e:
        print(f"Query API test failed: {e}")
        return False

def test_web_interface():
    """Test web interface"""
    print("\nTesting web interface...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Web interface: {response.status_code}")
        if response.status_code == 200:
            print("Web interface is accessible")
            return True
        else:
            print(f"Web interface error: {response.status_code}")
            return False
    except Exception as e:
        print(f"Web interface test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Distributed Tracing Tool")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health),
        ("Trace Submission", test_trace_submission),
        ("Query API", test_query_api),
        ("Web Interface", test_web_interface)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        print(f"✅ PASSED" if success else "❌ FAILED")
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The distributed tracing tool is working correctly.")
        print(f"🌐 Web interface: {BASE_URL}")
        print(f"📊 API documentation: {BASE_URL}/api/v1")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the application.")

if __name__ == "__main__":
    main()
