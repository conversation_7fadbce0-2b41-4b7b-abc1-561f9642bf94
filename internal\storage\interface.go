package storage

import (
	"context"
	"time"

	"distributed-tracing-tool/internal/models"
)

// Storage defines the interface for trace storage backends
type Storage interface {
	// Initialize the storage backend
	Initialize(ctx context.Context) error
	
	// Close the storage backend
	Close() error
	
	// Health check
	HealthCheck(ctx context.Context) error
	
	// Trace operations
	WriteTrace(ctx context.Context, trace *models.Trace) error
	WriteBatch(ctx context.Context, traces []*models.Trace) error
	GetTrace(ctx context.Context, traceID models.TraceID) (*models.Trace, error)
	
	// Span operations
	WriteSpan(ctx context.Context, span *models.Span) error
	WriteSpanBatch(ctx context.Context, spans []*models.Span) error
	GetSpan(ctx context.Context, traceID models.TraceID, spanID models.SpanID) (*models.Span, error)
	GetTraceSpans(ctx context.Context, traceID models.TraceID) ([]*models.Span, error)
	
	// Query operations
	FindTraces(ctx context.Context, query *TraceQuery) ([]*models.Trace, error)
	FindSpans(ctx context.Context, query *SpanQuery) ([]*models.Span, error)
	
	// Service operations
	GetServices(ctx context.Context) ([]string, error)
	GetOperations(ctx context.Context, service string) ([]string, error)
	
	// Metrics and statistics
	GetTraceStats(ctx context.Context, traceID models.TraceID) (*TraceStats, error)
	GetServiceStats(ctx context.Context, service string, timeRange TimeRange) (*ServiceStats, error)
	
	// Maintenance operations
	DeleteOldTraces(ctx context.Context, before time.Time) (int64, error)
	GetStorageStats(ctx context.Context) (*StorageStats, error)
}

// TraceQuery represents a query for traces
type TraceQuery struct {
	// Service and operation filters
	Service   string `json:"service,omitempty"`
	Operation string `json:"operation,omitempty"`
	
	// Time range
	StartTime time.Time `json:"startTime,omitempty"`
	EndTime   time.Time `json:"endTime,omitempty"`
	
	// Duration filters (in microseconds)
	MinDuration int64 `json:"minDuration,omitempty"`
	MaxDuration int64 `json:"maxDuration,omitempty"`
	
	// Tag filters
	Tags map[string]interface{} `json:"tags,omitempty"`
	
	// Error filter
	HasError *bool `json:"hasError,omitempty"`
	
	// Pagination
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
	
	// Sorting
	SortBy    string `json:"sortBy,omitempty"`    // startTime, duration, service
	SortOrder string `json:"sortOrder,omitempty"` // asc, desc
}

// SpanQuery represents a query for spans
type SpanQuery struct {
	// Trace filter
	TraceID models.TraceID `json:"traceID,omitempty"`
	
	// Service and operation filters
	Service   string `json:"service,omitempty"`
	Operation string `json:"operation,omitempty"`
	
	// Time range
	StartTime time.Time `json:"startTime,omitempty"`
	EndTime   time.Time `json:"endTime,omitempty"`
	
	// Duration filters (in microseconds)
	MinDuration int64 `json:"minDuration,omitempty"`
	MaxDuration int64 `json:"maxDuration,omitempty"`
	
	// Tag filters
	Tags map[string]interface{} `json:"tags,omitempty"`
	
	// Error filter
	HasError *bool `json:"hasError,omitempty"`
	
	// Pagination
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// TimeRange represents a time range for queries
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// TraceStats represents statistics for a trace
type TraceStats struct {
	TraceID      models.TraceID `json:"traceID"`
	SpanCount    int            `json:"spanCount"`
	ServiceCount int            `json:"serviceCount"`
	ErrorCount   int            `json:"errorCount"`
	Duration     int64          `json:"duration"`
	StartTime    time.Time      `json:"startTime"`
	EndTime      time.Time      `json:"endTime"`
}

// ServiceStats represents statistics for a service
type ServiceStats struct {
	Service       string        `json:"service"`
	RequestCount  int64         `json:"requestCount"`
	ErrorCount    int64         `json:"errorCount"`
	ErrorRate     float64       `json:"errorRate"`
	AvgDuration   int64         `json:"avgDuration"`
	P95Duration   int64         `json:"p95Duration"`
	P99Duration   int64         `json:"p99Duration"`
	TimeRange     TimeRange     `json:"timeRange"`
	Operations    []OpStats     `json:"operations,omitempty"`
}

// OpStats represents statistics for an operation
type OpStats struct {
	Operation     string  `json:"operation"`
	RequestCount  int64   `json:"requestCount"`
	ErrorCount    int64   `json:"errorCount"`
	ErrorRate     float64 `json:"errorRate"`
	AvgDuration   int64   `json:"avgDuration"`
	P95Duration   int64   `json:"p95Duration"`
	P99Duration   int64   `json:"p99Duration"`
}

// StorageStats represents storage backend statistics
type StorageStats struct {
	TraceCount     int64     `json:"traceCount"`
	SpanCount      int64     `json:"spanCount"`
	ServiceCount   int       `json:"serviceCount"`
	OperationCount int       `json:"operationCount"`
	OldestTrace    time.Time `json:"oldestTrace"`
	NewestTrace    time.Time `json:"newestTrace"`
	StorageSize    int64     `json:"storageSize"` // bytes
}

// WriteOptions provides options for write operations
type WriteOptions struct {
	// Batch size for batch operations
	BatchSize int
	
	// Timeout for write operations
	Timeout time.Duration
	
	// Whether to validate data before writing
	Validate bool
	
	// Whether to compress data
	Compress bool
}

// ReadOptions provides options for read operations
type ReadOptions struct {
	// Timeout for read operations
	Timeout time.Duration
	
	// Whether to include span details
	IncludeSpans bool
	
	// Whether to include process information
	IncludeProcesses bool
	
	// Whether to decompress data
	Decompress bool
}

// DefaultWriteOptions returns default write options
func DefaultWriteOptions() *WriteOptions {
	return &WriteOptions{
		BatchSize: 1000,
		Timeout:   30 * time.Second,
		Validate:  true,
		Compress:  false,
	}
}

// DefaultReadOptions returns default read options
func DefaultReadOptions() *ReadOptions {
	return &ReadOptions{
		Timeout:          10 * time.Second,
		IncludeSpans:     true,
		IncludeProcesses: true,
		Decompress:       false,
	}
}
