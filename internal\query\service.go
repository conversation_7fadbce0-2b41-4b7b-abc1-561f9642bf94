package query

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"distributed-tracing-tool/internal/models"
	"distributed-tracing-tool/internal/storage"
	"distributed-tracing-tool/pkg/logger"
)

// Service provides query functionality for traces and spans
type Service struct {
	logger  *logger.Logger
	storage storage.Storage
	config  *Config
}

// Config holds query service configuration
type Config struct {
	MaxResults      int           `json:"maxResults"`
	DefaultPageSize int           `json:"defaultPageSize"`
	CacheEnabled    bool          `json:"cacheEnabled"`
	CacheTTL        time.Duration `json:"cacheTTL"`
	QueryTimeout    time.Duration `json:"queryTimeout"`
}

// DefaultConfig returns default query service configuration
func DefaultConfig() *Config {
	return &Config{
		MaxResults:      10000,
		DefaultPageSize: 100,
		CacheEnabled:    true,
		CacheTTL:        5 * time.Minute,
		QueryTimeout:    30 * time.Second,
	}
}

// New creates a new query service
func New(logger *logger.Logger, storage storage.Storage, config *Config) *Service {
	if config == nil {
		config = DefaultConfig()
	}
	
	return &Service{
		logger:  logger,
		storage: storage,
		config:  config,
	}
}

// GetTrace retrieves a trace by ID
func (s *Service) GetTrace(ctx context.Context, traceID string) (*models.Trace, error) {
	if traceID == "" {
		return nil, fmt.Errorf("trace ID cannot be empty")
	}
	
	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	s.logger.WithField("trace_id", traceID).Debug("Getting trace")
	
	trace, err := s.storage.GetTrace(ctx, models.TraceID(traceID))
	if err != nil {
		return nil, fmt.Errorf("failed to get trace: %w", err)
	}
	
	return trace, nil
}

// FindTraces searches for traces based on query parameters
func (s *Service) FindTraces(ctx context.Context, params *TraceQueryParams) (*TraceSearchResult, error) {
	// Validate parameters
	if err := s.validateTraceQueryParams(params); err != nil {
		return nil, fmt.Errorf("invalid query parameters: %w", err)
	}
	
	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	// Convert to storage query
	query := s.buildStorageTraceQuery(params)
	
	s.logger.WithFields(map[string]interface{}{
		"service":   params.Service,
		"operation": params.Operation,
		"limit":     params.Limit,
	}).Debug("Finding traces")
	
	// Execute query
	traces, err := s.storage.FindTraces(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to find traces: %w", err)
	}
	
	// Build result
	result := &TraceSearchResult{
		Traces: traces,
		Total:  len(traces),
		Limit:  params.Limit,
		Offset: params.Offset,
	}
	
	return result, nil
}

// GetServices returns all available services
func (s *Service) GetServices(ctx context.Context) ([]string, error) {
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	s.logger.Debug("Getting services")
	
	services, err := s.storage.GetServices(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get services: %w", err)
	}
	
	return services, nil
}

// GetOperations returns all operations for a service
func (s *Service) GetOperations(ctx context.Context, service string) ([]string, error) {
	if service == "" {
		return nil, fmt.Errorf("service name cannot be empty")
	}
	
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	s.logger.WithField("service", service).Debug("Getting operations")
	
	operations, err := s.storage.GetOperations(ctx, service)
	if err != nil {
		return nil, fmt.Errorf("failed to get operations: %w", err)
	}
	
	return operations, nil
}

// GetTraceStats returns statistics for a trace
func (s *Service) GetTraceStats(ctx context.Context, traceID string) (*storage.TraceStats, error) {
	if traceID == "" {
		return nil, fmt.Errorf("trace ID cannot be empty")
	}
	
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	s.logger.WithField("trace_id", traceID).Debug("Getting trace stats")
	
	stats, err := s.storage.GetTraceStats(ctx, models.TraceID(traceID))
	if err != nil {
		return nil, fmt.Errorf("failed to get trace stats: %w", err)
	}
	
	return stats, nil
}

// GetServiceStats returns statistics for a service
func (s *Service) GetServiceStats(ctx context.Context, service string, timeRange *TimeRange) (*storage.ServiceStats, error) {
	if service == "" {
		return nil, fmt.Errorf("service name cannot be empty")
	}
	
	// Use default time range if not provided
	if timeRange == nil {
		now := time.Now()
		timeRange = &TimeRange{
			Start: now.Add(-24 * time.Hour),
			End:   now,
		}
	}
	
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	s.logger.WithFields(map[string]interface{}{
		"service": service,
		"start":   timeRange.Start,
		"end":     timeRange.End,
	}).Debug("Getting service stats")
	
	storageTimeRange := storage.TimeRange{
		Start: timeRange.Start,
		End:   timeRange.End,
	}
	
	stats, err := s.storage.GetServiceStats(ctx, service, storageTimeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get service stats: %w", err)
	}
	
	return stats, nil
}

// GetStorageStats returns storage statistics
func (s *Service) GetStorageStats(ctx context.Context) (*storage.StorageStats, error) {
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()
	
	s.logger.Debug("Getting storage stats")
	
	stats, err := s.storage.GetStorageStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get storage stats: %w", err)
	}
	
	return stats, nil
}

// validateTraceQueryParams validates trace query parameters
func (s *Service) validateTraceQueryParams(params *TraceQueryParams) error {
	if params.Limit < 0 {
		return fmt.Errorf("limit cannot be negative")
	}
	if params.Limit > s.config.MaxResults {
		return fmt.Errorf("limit cannot exceed %d", s.config.MaxResults)
	}
	if params.Offset < 0 {
		return fmt.Errorf("offset cannot be negative")
	}
	if params.MinDuration < 0 {
		return fmt.Errorf("min duration cannot be negative")
	}
	if params.MaxDuration < 0 {
		return fmt.Errorf("max duration cannot be negative")
	}
	if params.MinDuration > 0 && params.MaxDuration > 0 && params.MinDuration > params.MaxDuration {
		return fmt.Errorf("min duration cannot be greater than max duration")
	}
	if !params.StartTime.IsZero() && !params.EndTime.IsZero() && params.StartTime.After(params.EndTime) {
		return fmt.Errorf("start time cannot be after end time")
	}
	
	return nil
}

// buildStorageTraceQuery converts query parameters to storage query
func (s *Service) buildStorageTraceQuery(params *TraceQueryParams) *storage.TraceQuery {
	query := &storage.TraceQuery{
		Service:     params.Service,
		Operation:   params.Operation,
		StartTime:   params.StartTime,
		EndTime:     params.EndTime,
		MinDuration: params.MinDuration,
		MaxDuration: params.MaxDuration,
		Tags:        params.Tags,
		HasError:    params.HasError,
		Limit:       params.Limit,
		Offset:      params.Offset,
		SortBy:      params.SortBy,
		SortOrder:   params.SortOrder,
	}
	
	// Set default limit if not specified
	if query.Limit == 0 {
		query.Limit = s.config.DefaultPageSize
	}
	
	// Set default sort order
	if query.SortBy == "" {
		query.SortBy = "startTime"
	}
	if query.SortOrder == "" {
		query.SortOrder = "desc"
	}
	
	return query
}

// ParseDuration parses a duration string (e.g., "100ms", "5s", "2m")
func (s *Service) ParseDuration(durationStr string) (int64, error) {
	if durationStr == "" {
		return 0, nil
	}
	
	// Try parsing as microseconds first
	if microseconds, err := strconv.ParseInt(durationStr, 10, 64); err == nil {
		return microseconds, nil
	}
	
	// Try parsing as duration string
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		return 0, fmt.Errorf("invalid duration format: %s", durationStr)
	}
	
	return duration.Microseconds(), nil
}
