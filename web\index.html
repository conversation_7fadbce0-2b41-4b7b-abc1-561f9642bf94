<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Distributed Tracing Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            margin-bottom: 1rem;
            color: #2c3e50;
            font-size: 1.25rem;
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input, .form-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .results {
            margin-top: 1rem;
        }
        
        .trace-item {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .trace-item:hover {
            background: #f8f9fa;
        }
        
        .trace-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .trace-id {
            font-family: monospace;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .trace-duration {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .trace-services {
            font-size: 0.85rem;
            color: #555;
        }
        
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 0.75rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            font-size: 0.85rem;
            color: #7f8c8d;
            margin-top: 0.25rem;
        }
        
        .trace-detail {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .span-item {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: white;
            border-left: 3px solid #3498db;
            border-radius: 0 4px 4px 0;
        }
        
        .span-operation {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .span-service {
            font-size: 0.85rem;
            color: #7f8c8d;
        }
        
        .span-duration {
            font-size: 0.85rem;
            color: #27ae60;
        }
        
        .span-error {
            border-left-color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Distributed Tracing Tool</h1>
    </div>
    
    <div class="container">
        <!-- System Stats -->
        <div class="card">
            <h2>System Overview</h2>
            <div class="stats-grid" id="systemStats">
                <div class="stat-item">
                    <div class="stat-value" id="traceCount">-</div>
                    <div class="stat-label">Traces</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="spanCount">-</div>
                    <div class="stat-label">Spans</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="serviceCount">-</div>
                    <div class="stat-label">Services</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="operationCount">-</div>
                    <div class="stat-label">Operations</div>
                </div>
            </div>
        </div>
        
        <!-- Search -->
        <div class="card">
            <h2>Search Traces</h2>
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label for="service">Service</label>
                    <select id="service">
                        <option value="">All Services</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="operation">Operation</label>
                    <input type="text" id="operation" placeholder="Operation name">
                </div>
                <div class="form-group">
                    <label for="minDuration">Min Duration</label>
                    <input type="text" id="minDuration" placeholder="e.g., 100ms">
                </div>
                <div class="form-group">
                    <label for="maxDuration">Max Duration</label>
                    <input type="text" id="maxDuration" placeholder="e.g., 5s">
                </div>
                <div class="form-group">
                    <label for="hasError">Has Error</label>
                    <select id="hasError">
                        <option value="">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="limit">Limit</label>
                    <input type="number" id="limit" value="20" min="1" max="1000">
                </div>
            </form>
            <button class="btn" onclick="searchTraces()">Search</button>
            <button class="btn btn-secondary" onclick="clearSearch()">Clear</button>
        </div>
        
        <!-- Results -->
        <div class="card">
            <h2>Search Results</h2>
            <div id="results">
                <div class="loading">Click "Search" to find traces</div>
            </div>
        </div>
    </div>
    
    <script>
        const API_BASE = '/api/v1';
        
        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStats();
            loadServices();
        });
        
        async function loadSystemStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const stats = await response.json();
                
                document.getElementById('traceCount').textContent = stats.traceCount || 0;
                document.getElementById('spanCount').textContent = stats.spanCount || 0;
                document.getElementById('serviceCount').textContent = stats.serviceCount || 0;
                document.getElementById('operationCount').textContent = stats.operationCount || 0;
            } catch (error) {
                console.error('Failed to load system stats:', error);
            }
        }
        
        async function loadServices() {
            try {
                const response = await fetch(`${API_BASE}/services`);
                const data = await response.json();
                const services = data.services || [];
                
                const serviceSelect = document.getElementById('service');
                serviceSelect.innerHTML = '<option value="">All Services</option>';
                
                services.forEach(service => {
                    const option = document.createElement('option');
                    option.value = service;
                    option.textContent = service;
                    serviceSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Failed to load services:', error);
            }
        }
        
        async function searchTraces() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">Searching...</div>';
            
            try {
                const params = new URLSearchParams();
                
                const service = document.getElementById('service').value;
                const operation = document.getElementById('operation').value;
                const minDuration = document.getElementById('minDuration').value;
                const maxDuration = document.getElementById('maxDuration').value;
                const hasError = document.getElementById('hasError').value;
                const limit = document.getElementById('limit').value;
                
                if (service) params.append('service', service);
                if (operation) params.append('operation', operation);
                if (minDuration) params.append('minDuration', minDuration);
                if (maxDuration) params.append('maxDuration', maxDuration);
                if (hasError) params.append('hasError', hasError);
                if (limit) params.append('limit', limit);
                
                const response = await fetch(`${API_BASE}/traces?${params}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Search failed');
                }
                
                displayTraces(data.traces || []);
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        function displayTraces(traces) {
            const resultsDiv = document.getElementById('results');
            
            if (traces.length === 0) {
                resultsDiv.innerHTML = '<div class="loading">No traces found</div>';
                return;
            }
            
            const html = traces.map(trace => `
                <div class="trace-item" onclick="toggleTraceDetail('${trace.traceID}')">
                    <div class="trace-header">
                        <span class="trace-id">${trace.traceID}</span>
                        <span class="trace-duration">${formatDuration(trace.duration)}</span>
                    </div>
                    <div class="trace-services">
                        ${trace.spanCount} spans • ${trace.serviceCount} services
                        ${trace.errorCount > 0 ? `• <span style="color: #e74c3c">${trace.errorCount} errors</span>` : ''}
                    </div>
                    <div class="trace-detail" id="detail-${trace.traceID}">
                        <div class="loading">Loading trace details...</div>
                    </div>
                </div>
            `).join('');
            
            resultsDiv.innerHTML = html;
        }
        
        async function toggleTraceDetail(traceID) {
            const detailDiv = document.getElementById(`detail-${traceID}`);
            
            if (detailDiv.style.display === 'block') {
                detailDiv.style.display = 'none';
                return;
            }
            
            detailDiv.style.display = 'block';
            
            try {
                const response = await fetch(`${API_BASE}/traces/${traceID}`);
                const trace = await response.json();
                
                if (!response.ok) {
                    throw new Error(trace.error || 'Failed to load trace');
                }
                
                const spansHtml = trace.spans.map(span => `
                    <div class="span-item ${span.statusCode >= 400 ? 'span-error' : ''}">
                        <div class="span-operation">${span.operationName}</div>
                        <div class="span-service">
                            ${trace.processes[span.processID]?.serviceName || 'Unknown Service'}
                        </div>
                        <div class="span-duration">${formatDuration(span.duration)}</div>
                    </div>
                `).join('');
                
                detailDiv.innerHTML = spansHtml;
            } catch (error) {
                detailDiv.innerHTML = `<div class="error">Error loading trace: ${error.message}</div>`;
            }
        }
        
        function formatDuration(microseconds) {
            if (microseconds < 1000) {
                return `${microseconds}μs`;
            } else if (microseconds < 1000000) {
                return `${(microseconds / 1000).toFixed(1)}ms`;
            } else {
                return `${(microseconds / 1000000).toFixed(2)}s`;
            }
        }
        
        function clearSearch() {
            document.getElementById('searchForm').reset();
            document.getElementById('results').innerHTML = '<div class="loading">Click "Search" to find traces</div>';
        }
    </script>
</body>
</html>
