package query

import (
	"time"

	"distributed-tracing-tool/internal/models"
)

// TraceQueryParams represents parameters for trace queries
type TraceQueryParams struct {
	// Service and operation filters
	Service   string `json:"service,omitempty" form:"service"`
	Operation string `json:"operation,omitempty" form:"operation"`
	
	// Time range filters
	StartTime time.Time `json:"startTime,omitempty" form:"start"`
	EndTime   time.Time `json:"endTime,omitempty" form:"end"`
	
	// Duration filters (in microseconds)
	MinDuration int64 `json:"minDuration,omitempty" form:"minDuration"`
	MaxDuration int64 `json:"maxDuration,omitempty" form:"maxDuration"`
	
	// Tag filters
	Tags map[string]interface{} `json:"tags,omitempty"`
	
	// Error filter
	HasError *bool `json:"hasError,omitempty" form:"hasError"`
	
	// Pagination
	Limit  int `json:"limit,omitempty" form:"limit"`
	Offset int `json:"offset,omitempty" form:"offset"`
	
	// Sorting
	SortBy    string `json:"sortBy,omitempty" form:"sortBy"`       // startTime, duration, service
	SortOrder string `json:"sortOrder,omitempty" form:"sortOrder"` // asc, desc
}

// SpanQueryParams represents parameters for span queries
type SpanQueryParams struct {
	// Trace filter
	TraceID string `json:"traceID,omitempty" form:"traceId"`
	
	// Service and operation filters
	Service   string `json:"service,omitempty" form:"service"`
	Operation string `json:"operation,omitempty" form:"operation"`
	
	// Time range filters
	StartTime time.Time `json:"startTime,omitempty" form:"start"`
	EndTime   time.Time `json:"endTime,omitempty" form:"end"`
	
	// Duration filters (in microseconds)
	MinDuration int64 `json:"minDuration,omitempty" form:"minDuration"`
	MaxDuration int64 `json:"maxDuration,omitempty" form:"maxDuration"`
	
	// Tag filters
	Tags map[string]interface{} `json:"tags,omitempty"`
	
	// Error filter
	HasError *bool `json:"hasError,omitempty" form:"hasError"`
	
	// Pagination
	Limit  int `json:"limit,omitempty" form:"limit"`
	Offset int `json:"offset,omitempty" form:"offset"`
}

// TimeRange represents a time range for queries
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// TraceSearchResult represents the result of a trace search
type TraceSearchResult struct {
	Traces []*models.Trace `json:"traces"`
	Total  int             `json:"total"`
	Limit  int             `json:"limit"`
	Offset int             `json:"offset"`
}

// SpanSearchResult represents the result of a span search
type SpanSearchResult struct {
	Spans  []*models.Span `json:"spans"`
	Total  int            `json:"total"`
	Limit  int            `json:"limit"`
	Offset int            `json:"offset"`
}

// ServiceInfo represents information about a service
type ServiceInfo struct {
	Name       string    `json:"name"`
	Operations []string  `json:"operations"`
	LastSeen   time.Time `json:"lastSeen"`
}

// OperationInfo represents information about an operation
type OperationInfo struct {
	Name     string    `json:"name"`
	Service  string    `json:"service"`
	LastSeen time.Time `json:"lastSeen"`
}

// TraceTimeline represents a trace timeline for visualization
type TraceTimeline struct {
	TraceID   models.TraceID    `json:"traceID"`
	StartTime time.Time         `json:"startTime"`
	EndTime   time.Time         `json:"endTime"`
	Duration  int64             `json:"duration"`
	Services  []string          `json:"services"`
	Spans     []*TimelineSpan   `json:"spans"`
	Processes map[string]string `json:"processes"` // processID -> serviceName
}

// TimelineSpan represents a span in the timeline view
type TimelineSpan struct {
	SpanID        models.SpanID `json:"spanID"`
	ParentSpanID  models.SpanID `json:"parentSpanID,omitempty"`
	OperationName string        `json:"operationName"`
	ServiceName   string        `json:"serviceName"`
	StartTime     int64         `json:"startTime"`
	Duration      int64         `json:"duration"`
	Tags          []models.KeyValue `json:"tags,omitempty"`
	HasError      bool          `json:"hasError"`
	Kind          string        `json:"kind,omitempty"`
	StatusCode    int32         `json:"statusCode,omitempty"`
	Level         int           `json:"level"` // Depth level in the trace tree
}

// ServiceDependency represents a dependency between services
type ServiceDependency struct {
	Parent    string `json:"parent"`
	Child     string `json:"child"`
	CallCount int64  `json:"callCount"`
}

// ServiceMap represents the service dependency map
type ServiceMap struct {
	Services     []string            `json:"services"`
	Dependencies []ServiceDependency `json:"dependencies"`
}

// ErrorSummary represents error information for a trace or service
type ErrorSummary struct {
	Count      int64             `json:"count"`
	Rate       float64           `json:"rate"`       // Error rate as percentage
	Types      map[string]int64  `json:"types"`      // Error type -> count
	Recent     []ErrorInstance   `json:"recent"`     // Recent error instances
}

// ErrorInstance represents a specific error instance
type ErrorInstance struct {
	TraceID     models.TraceID `json:"traceID"`
	SpanID      models.SpanID  `json:"spanID"`
	Service     string         `json:"service"`
	Operation   string         `json:"operation"`
	Message     string         `json:"message"`
	Timestamp   time.Time      `json:"timestamp"`
	StatusCode  int32          `json:"statusCode,omitempty"`
}

// PerformanceMetrics represents performance metrics for a service or operation
type PerformanceMetrics struct {
	RequestCount  int64   `json:"requestCount"`
	ErrorCount    int64   `json:"errorCount"`
	ErrorRate     float64 `json:"errorRate"`
	AvgDuration   int64   `json:"avgDuration"`   // microseconds
	P50Duration   int64   `json:"p50Duration"`   // microseconds
	P95Duration   int64   `json:"p95Duration"`   // microseconds
	P99Duration   int64   `json:"p99Duration"`   // microseconds
	MinDuration   int64   `json:"minDuration"`   // microseconds
	MaxDuration   int64   `json:"maxDuration"`   // microseconds
	Throughput    float64 `json:"throughput"`    // requests per second
}

// TrendData represents time-series data for metrics
type TrendData struct {
	Timestamps []time.Time `json:"timestamps"`
	Values     []float64   `json:"values"`
	Metric     string      `json:"metric"`
	Unit       string      `json:"unit"`
}

// Dashboard represents a monitoring dashboard
type Dashboard struct {
	Services    []ServiceInfo       `json:"services"`
	ServiceMap  ServiceMap          `json:"serviceMap"`
	Errors      ErrorSummary        `json:"errors"`
	Performance PerformanceMetrics  `json:"performance"`
	Trends      []TrendData         `json:"trends"`
	UpdatedAt   time.Time           `json:"updatedAt"`
}

// SearchSuggestion represents a search suggestion
type SearchSuggestion struct {
	Type  string `json:"type"`  // service, operation, tag
	Value string `json:"value"`
	Count int64  `json:"count"` // How many times this appears
}

// AutocompleteResult represents autocomplete results
type AutocompleteResult struct {
	Suggestions []SearchSuggestion `json:"suggestions"`
	Query       string             `json:"query"`
}
