package models

import (
	"errors"
	"fmt"
	"time"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error in field '%s': %s", e.Field, e.Message)
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	if len(e) == 0 {
		return "no validation errors"
	}
	if len(e) == 1 {
		return e[0].Error()
	}
	return fmt.Sprintf("%d validation errors: %s (and %d more)", len(e), e[0].Error(), len(e)-1)
}

// ValidateTrace validates a trace and returns any validation errors
func ValidateTrace(trace *Trace) error {
	var errors ValidationErrors
	
	// Validate trace ID
	if trace.TraceID == "" {
		errors = append(errors, ValidationError{
			Field:   "traceID",
			Message: "trace ID cannot be empty",
		})
	}
	
	// Validate spans
	if len(trace.Spans) == 0 {
		errors = append(errors, ValidationError{
			Field:   "spans",
			Message: "trace must have at least one span",
		})
	}
	
	// Validate each span
	spanIDs := make(map[SpanID]bool)
	for i, span := range trace.Spans {
		if spanErrors := ValidateSpan(&span); spanErrors != nil {
			if ve, ok := spanErrors.(ValidationErrors); ok {
				for _, err := range ve {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("spans[%d].%s", i, err.Field),
						Message: err.Message,
					})
				}
			} else {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("spans[%d]", i),
					Message: spanErrors.Error(),
				})
			}
		}
		
		// Check for duplicate span IDs
		if spanIDs[span.SpanID] {
			errors = append(errors, ValidationError{
				Field:   fmt.Sprintf("spans[%d].spanID", i),
				Message: "duplicate span ID in trace",
			})
		}
		spanIDs[span.SpanID] = true
		
		// Validate that span belongs to this trace
		if span.TraceID != trace.TraceID {
			errors = append(errors, ValidationError{
				Field:   fmt.Sprintf("spans[%d].traceID", i),
				Message: "span trace ID does not match trace ID",
			})
		}
	}
	
	// Validate processes
	for processID, process := range trace.Processes {
		if processErrors := ValidateProcess(&process); processErrors != nil {
			if ve, ok := processErrors.(ValidationErrors); ok {
				for _, err := range ve {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("processes[%s].%s", processID, err.Field),
						Message: err.Message,
					})
				}
			} else {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("processes[%s]", processID),
					Message: processErrors.Error(),
				})
			}
		}
	}
	
	// Validate timing
	if !trace.StartTime.IsZero() && !trace.EndTime.IsZero() {
		if trace.EndTime.Before(trace.StartTime) {
			errors = append(errors, ValidationError{
				Field:   "endTime",
				Message: "end time cannot be before start time",
			})
		}
		
		expectedDuration := trace.EndTime.Sub(trace.StartTime).Microseconds()
		if trace.Duration != expectedDuration {
			errors = append(errors, ValidationError{
				Field:   "duration",
				Message: fmt.Sprintf("duration (%d) does not match start/end time difference (%d)", trace.Duration, expectedDuration),
			})
		}
	}
	
	if len(errors) > 0 {
		return errors
	}
	return nil
}

// ValidateSpan validates a span and returns any validation errors
func ValidateSpan(span *Span) error {
	var errors ValidationErrors
	
	// Validate required fields
	if span.TraceID == "" {
		errors = append(errors, ValidationError{
			Field:   "traceID",
			Message: "trace ID cannot be empty",
		})
	}
	
	if span.SpanID == "" {
		errors = append(errors, ValidationError{
			Field:   "spanID",
			Message: "span ID cannot be empty",
		})
	}
	
	if span.OperationName == "" {
		errors = append(errors, ValidationError{
			Field:   "operationName",
			Message: "operation name cannot be empty",
		})
	}
	
	if span.ProcessID == "" {
		errors = append(errors, ValidationError{
			Field:   "processID",
			Message: "process ID cannot be empty",
		})
	}
	
	// Validate timing
	if span.StartTime <= 0 {
		errors = append(errors, ValidationError{
			Field:   "startTime",
			Message: "start time must be positive",
		})
	}
	
	if span.Duration < 0 {
		errors = append(errors, ValidationError{
			Field:   "duration",
			Message: "duration cannot be negative",
		})
	}
	
	// Validate span kind
	if span.Kind != "" {
		validKinds := map[string]bool{
			SpanKindUnspecified: true,
			SpanKindInternal:    true,
			SpanKindServer:      true,
			SpanKindClient:      true,
			SpanKindProducer:    true,
			SpanKindConsumer:    true,
		}
		if !validKinds[span.Kind] {
			errors = append(errors, ValidationError{
				Field:   "kind",
				Message: fmt.Sprintf("invalid span kind: %s", span.Kind),
			})
		}
	}
	
	// Validate tags
	for i, tag := range span.Tags {
		if tagErrors := ValidateKeyValue(&tag); tagErrors != nil {
			errors = append(errors, ValidationError{
				Field:   fmt.Sprintf("tags[%d]", i),
				Message: tagErrors.Error(),
			})
		}
	}
	
	// Validate logs
	for i, log := range span.Logs {
		if logErrors := ValidateLog(&log); logErrors != nil {
			if ve, ok := logErrors.(ValidationErrors); ok {
				for _, err := range ve {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("logs[%d].%s", i, err.Field),
						Message: err.Message,
					})
				}
			} else {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("logs[%d]", i),
					Message: logErrors.Error(),
				})
			}
		}
	}
	
	// Validate references
	for i, ref := range span.References {
		if refErrors := ValidateSpanRef(&ref); refErrors != nil {
			if ve, ok := refErrors.(ValidationErrors); ok {
				for _, err := range ve {
					errors = append(errors, ValidationError{
						Field:   fmt.Sprintf("references[%d].%s", i, err.Field),
						Message: err.Message,
					})
				}
			} else {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("references[%d]", i),
					Message: refErrors.Error(),
				})
			}
		}
	}
	
	if len(errors) > 0 {
		return errors
	}
	return nil
}

// ValidateProcess validates a process and returns any validation errors
func ValidateProcess(process *Process) error {
	var errors ValidationErrors
	
	if process.ServiceName == "" {
		errors = append(errors, ValidationError{
			Field:   "serviceName",
			Message: "service name cannot be empty",
		})
	}
	
	// Validate tags
	for i, tag := range process.Tags {
		if tagErrors := ValidateKeyValue(&tag); tagErrors != nil {
			errors = append(errors, ValidationError{
				Field:   fmt.Sprintf("tags[%d]", i),
				Message: tagErrors.Error(),
			})
		}
	}
	
	if len(errors) > 0 {
		return errors
	}
	return nil
}

// ValidateKeyValue validates a key-value pair
func ValidateKeyValue(kv *KeyValue) error {
	if kv.Key == "" {
		return errors.New("key cannot be empty")
	}
	
	if kv.Value == nil {
		return errors.New("value cannot be nil")
	}
	
	return nil
}

// ValidateLog validates a log entry
func ValidateLog(log *Log) error {
	var errors ValidationErrors
	
	if log.Timestamp <= 0 {
		errors = append(errors, ValidationError{
			Field:   "timestamp",
			Message: "timestamp must be positive",
		})
	}
	
	if len(log.Fields) == 0 {
		errors = append(errors, ValidationError{
			Field:   "fields",
			Message: "log must have at least one field",
		})
	}
	
	for i, field := range log.Fields {
		if fieldErrors := ValidateKeyValue(&field); fieldErrors != nil {
			errors = append(errors, ValidationError{
				Field:   fmt.Sprintf("fields[%d]", i),
				Message: fieldErrors.Error(),
			})
		}
	}
	
	if len(errors) > 0 {
		return errors
	}
	return nil
}

// ValidateSpanRef validates a span reference
func ValidateSpanRef(ref *SpanRef) error {
	var errors ValidationErrors
	
	if ref.RefType == "" {
		errors = append(errors, ValidationError{
			Field:   "refType",
			Message: "reference type cannot be empty",
		})
	} else if ref.RefType != ChildOf && ref.RefType != FollowsFrom {
		errors = append(errors, ValidationError{
			Field:   "refType",
			Message: fmt.Sprintf("invalid reference type: %s", ref.RefType),
		})
	}
	
	if ref.TraceID == "" {
		errors = append(errors, ValidationError{
			Field:   "traceID",
			Message: "trace ID cannot be empty",
		})
	}
	
	if ref.SpanID == "" {
		errors = append(errors, ValidationError{
			Field:   "spanID",
			Message: "span ID cannot be empty",
		})
	}
	
	if len(errors) > 0 {
		return errors
	}
	return nil
}

// SanitizeTrace cleans and normalizes trace data
func SanitizeTrace(trace *Trace) {
	// Set default values
	if trace.StartTime.IsZero() && len(trace.Spans) > 0 {
		// Find earliest span start time
		earliest := int64(0)
		for _, span := range trace.Spans {
			if earliest == 0 || span.StartTime < earliest {
				earliest = span.StartTime
			}
		}
		if earliest > 0 {
			trace.StartTime = time.Unix(0, earliest*1000) // convert microseconds to nanoseconds
		}
	}
	
	if trace.EndTime.IsZero() && len(trace.Spans) > 0 {
		// Find latest span end time
		latest := int64(0)
		for _, span := range trace.Spans {
			endTime := span.StartTime + span.Duration
			if endTime > latest {
				latest = endTime
			}
		}
		if latest > 0 {
			trace.EndTime = time.Unix(0, latest*1000) // convert microseconds to nanoseconds
		}
	}
	
	// Calculate duration if not set
	if trace.Duration == 0 && !trace.StartTime.IsZero() && !trace.EndTime.IsZero() {
		trace.Duration = trace.EndTime.Sub(trace.StartTime).Microseconds()
	}
	
	// Count services, spans, and errors
	services := make(map[string]bool)
	errorCount := 0
	
	for _, span := range trace.Spans {
		// Count services
		if process, exists := trace.Processes[span.ProcessID]; exists {
			services[process.ServiceName] = true
		}
		
		// Count errors
		if span.HasError() {
			errorCount++
		}
		
		// Sanitize span
		SanitizeSpan(&span)
	}
	
	trace.ServiceCount = len(services)
	trace.SpanCount = len(trace.Spans)
	trace.ErrorCount = errorCount
}

// SanitizeSpan cleans and normalizes span data
func SanitizeSpan(span *Span) {
	// Set default span kind
	if span.Kind == "" {
		span.Kind = SpanKindInternal
	}
	
	// Ensure tags have types
	for i := range span.Tags {
		if span.Tags[i].Type == "" {
			span.Tags[i].Type = getValueType(span.Tags[i].Value)
		}
	}
	
	// Ensure log fields have types
	for i := range span.Logs {
		for j := range span.Logs[i].Fields {
			if span.Logs[i].Fields[j].Type == "" {
				span.Logs[i].Fields[j].Type = getValueType(span.Logs[i].Fields[j].Value)
			}
		}
	}
}
