package app

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"distributed-tracing-tool/internal/collector"
	"distributed-tracing-tool/internal/config"
	"distributed-tracing-tool/internal/query"
	"distributed-tracing-tool/internal/storage"
	"distributed-tracing-tool/pkg/logger"

	"github.com/gin-gonic/gin"
)

// App represents the main application
type App struct {
	config *config.Config
	logger *logger.Logger

	// HTTP server
	httpServer *http.Server

	// Storage backend
	storage storage.Storage

	// Collector manager
	collectorManager *collector.Manager

	// Query service
	queryService *query.Service
}

// New creates a new application instance
func New(cfg *config.Config, log *logger.Logger) (*App, error) {
	app := &App{
		config: cfg,
		logger: log,
	}

	// Initialize storage backend
	storageFactory := storage.NewFactory(log)
	if err := storageFactory.ValidateConfig(cfg); err != nil {
		return nil, fmt.Errorf("invalid storage configuration: %w", err)
	}

	storageBackend, err := storageFactory.InitializeStorage(context.Background(), cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize storage: %w", err)
	}
	app.storage = storageBackend

	// Initialize collector manager
	collectorManager, err := collector.NewManager(log, cfg, storageBackend)
	if err != nil {
		return nil, fmt.Errorf("failed to create collector manager: %w", err)
	}
	app.collectorManager = collectorManager

	// Initialize query service
	queryConfig := &query.Config{
		MaxResults:      cfg.Query.MaxResults,
		DefaultPageSize: cfg.Query.DefaultPageSize,
		CacheEnabled:    cfg.Query.CacheEnabled,
		CacheTTL:        time.Duration(cfg.Query.CacheTTL) * time.Second,
		QueryTimeout:    30 * time.Second,
	}
	app.queryService = query.New(log.WithComponent("query"), storageBackend, queryConfig)

	// Initialize HTTP server
	if err := app.initHTTPServer(); err != nil {
		return nil, fmt.Errorf("failed to initialize HTTP server: %w", err)
	}

	return app, nil
}

// Run starts the application and blocks until context is cancelled
func (a *App) Run(ctx context.Context) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 2)
	
	// Start collector manager if available
	if a.collectorManager != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := a.collectorManager.Start(ctx); err != nil {
				errChan <- fmt.Errorf("collector manager error: %w", err)
			}
		}()
	}

	// Start HTTP server
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := a.startHTTPServer(); err != nil && err != http.ErrServerClosed {
			errChan <- fmt.Errorf("HTTP server error: %w", err)
		}
	}()
	
	// Wait for shutdown signal or error
	select {
	case <-ctx.Done():
		a.logger.Info("Shutting down application...")

		// Stop collector manager
		if a.collectorManager != nil {
			if err := a.collectorManager.Stop(); err != nil {
				a.logger.WithError(err).Error("Failed to stop collector manager gracefully")
			}
		}

		// Close storage
		if a.storage != nil {
			if err := a.storage.Close(); err != nil {
				a.logger.WithError(err).Error("Failed to close storage gracefully")
			}
		}

		// Shutdown HTTP server
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := a.httpServer.Shutdown(shutdownCtx); err != nil {
			a.logger.WithError(err).Error("Failed to shutdown HTTP server gracefully")
		}

		// Wait for all goroutines to finish
		wg.Wait()
		return nil
		
	case err := <-errChan:
		return err
	}
}

// initHTTPServer initializes the HTTP server
func (a *App) initHTTPServer() error {
	// Set Gin mode
	if a.config.LogLevel == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}
	
	// Create Gin router
	router := gin.New()
	
	// Add middleware
	router.Use(gin.Recovery())
	router.Use(a.loggingMiddleware())
	router.Use(a.corsMiddleware())
	
	// Setup routes
	a.setupRoutes(router)
	
	// Create HTTP server
	a.httpServer = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", a.config.Server.Host, a.config.Server.HTTPPort),
		Handler: router,
	}
	
	return nil
}

// startHTTPServer starts the HTTP server
func (a *App) startHTTPServer() error {
	a.logger.WithField("addr", a.httpServer.Addr).Info("Starting HTTP server")
	return a.httpServer.ListenAndServe()
}

// setupRoutes sets up HTTP routes
func (a *App) setupRoutes(router *gin.Engine) {
	// Health check endpoint
	router.GET("/health", a.healthHandler)
	
	// API routes
	api := router.Group("/api/v1")
	{
		// Register query API routes
		if a.queryService != nil {
			queryHandlers := query.NewHandlers(a.queryService, a.logger.WithComponent("query-handlers"))
			queryHandlers.RegisterRoutes(api)
		} else {
			// Placeholder endpoints
			api.GET("/traces", a.placeholderHandler("GET /traces"))
			api.GET("/traces/:id", a.placeholderHandler("GET /traces/:id"))
			api.GET("/services", a.placeholderHandler("GET /services"))
			api.GET("/operations", a.placeholderHandler("GET /operations"))
		}
	}
	
	// Register collector routes if available
	if a.collectorManager != nil {
		a.collectorManager.RegisterHTTPRoutes(router)
	} else {
		// Placeholder collector endpoints
		collector := router.Group("/api/collector")
		{
			collector.POST("/traces", a.placeholderHandler("POST /traces"))
			collector.POST("/spans", a.placeholderHandler("POST /spans"))
			collector.POST("/batch", a.placeholderHandler("POST /batch"))
		}
	}
	
	// Serve static files for web UI (if enabled)
	if a.config.WebUI.Enabled {
		router.Static("/static", a.config.WebUI.StaticPath)
		router.GET("/", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/static/index.html")
		})
	}
}

// healthHandler handles health check requests
func (a *App) healthHandler(c *gin.Context) {
	status := "ok"
	statusCode := http.StatusOK

	health := gin.H{
		"status":    status,
		"timestamp": time.Now().UTC(),
		"version":   "dev", // TODO: get from build info
		"components": gin.H{},
	}

	// Check storage health
	if a.storage != nil {
		if err := a.storage.HealthCheck(c.Request.Context()); err != nil {
			status = "degraded"
			statusCode = http.StatusServiceUnavailable
			health["components"].(gin.H)["storage"] = gin.H{
				"status": "unhealthy",
				"error":  err.Error(),
			}
		} else {
			health["components"].(gin.H)["storage"] = gin.H{
				"status": "healthy",
			}
		}
	}

	// Check collector health
	if a.collectorManager != nil {
		if a.collectorManager.IsHealthy() {
			health["components"].(gin.H)["collector"] = gin.H{
				"status": "healthy",
			}
		} else {
			status = "degraded"
			if statusCode == http.StatusOK {
				statusCode = http.StatusServiceUnavailable
			}
			health["components"].(gin.H)["collector"] = gin.H{
				"status": "unhealthy",
			}
		}
	}

	health["status"] = status
	c.JSON(statusCode, health)
}

// placeholderHandler creates a placeholder handler for endpoints not yet implemented
func (a *App) placeholderHandler(endpoint string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusNotImplemented, gin.H{
			"error":   "Not implemented",
			"endpoint": endpoint,
			"message": "This endpoint will be implemented in a future version",
		})
	}
}

// loggingMiddleware provides request logging
func (a *App) loggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		a.logger.WithFields(map[string]interface{}{
			"method":     param.Method,
			"path":       param.Path,
			"status":     param.StatusCode,
			"latency":    param.Latency,
			"client_ip":  param.ClientIP,
			"user_agent": param.Request.UserAgent(),
		}).Info("HTTP request")
		return ""
	})
}

// corsMiddleware provides CORS support
func (a *App) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}
