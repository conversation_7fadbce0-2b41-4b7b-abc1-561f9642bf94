version: '3.8'

services:
  tracing-tool:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"  # HTTP API
      - "9090:9090"  # gRPC collector
    volumes:
      - ./data:/app/data
      - ./config.yaml:/app/config.yaml:ro
    environment:
      - TRACING_LOG_LEVEL=info
      - TRACING_STORAGE_DSN=/app/data/traces.db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: ClickHouse for high-performance storage
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    ports:
      - "8123:8123"  # HTTP interface
      - "9000:9000"  # Native interface
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    environment:
      - CLICKHOUSE_DB=tracing
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_PASSWORD=
    profiles:
      - clickhouse

  # Optional: PostgreSQL for traditional relational storage
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=tracing
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    profiles:
      - postgres

volumes:
  clickhouse_data:
  postgres_data:
